// Angular Core
import { ChangeDetectionStrategy, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';

// NgRx
import { Store } from "@ngrx/store";
import { ComponentStore } from '@ngrx/component-store';

// Third-party Libraries
import { GridOptions } from 'ag-grid-enterprise';
import { ErrorMessages } from 'blg-akaun-ts-lib';
import { ToastrService } from 'ngx-toastr';
import { Observable, from } from 'rxjs';
import { map, mergeMap, take, toArray } from 'rxjs/operators';
import { SubSink } from 'subsink2';

// Session Controller
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionActions } from 'projects/shared-utilities/modules/session/session-controller/actions';

// Permissions
import { ClientSidePermissionsSelectors } from "projects/shared-utilities/modules/permission/client-side-permissions-controller/selectors";
import { PermissionStates } from 'projects/shared-utilities/modules/permission/permission-controller';
import { UserPermInquirySelectors } from 'projects/shared-utilities/modules/permission/user-permissions-inquiry-controller/selectors';

// Shared Utilities
import { SearchQueryModel } from 'projects/shared-utilities/models/query.model';
import { UtilitiesModule } from 'projects/shared-utilities/utilities/utilities.module';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { AppConfig } from 'projects/shared-utilities/visa';

// Application Imports
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { SalesReportByItemCodeSearchModel } from '../../../models/advanced-search-models/sales-report-by-item-code-search.model';
import { SalesReportByItemCodeInputModel, SalesReportByItemCodeModel } from '../../../models/sales-report-by-item-code-model';
import { ApiService } from '../../../services/api-service';

interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
}

@Component({
  selector: 'app-sales-report-by-item-code',
  templateUrl: './sales-report-by-item-code.component.html',
  styleUrls: ['./sales-report-by-item-code.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})

export class SalesReportByItemCodeComponent extends ViewColumnComponent implements OnInit, OnDestroy {

  protected subs = new SubSink();

  compId = 'salesReportByItemCode';
  compName = 'Sales Report By Item Code';
  protected readonly index = 0;
  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  protected localState: LocalState;

  readonly deactivateAdd$ = this.componentStore.select(state => state.deactivateAdd);
  readonly deactivateList$ = this.componentStore.select(state => state.deactivateList);

  readonly userPermissionTarget$ = this.permissionStore.select(
    UserPermInquirySelectors.selectUserPermInquiry
  );

  toggleColumn$: Observable<boolean>;
  searchModel = SalesReportByItemCodeSearchModel;

  showColumns = [
    { name: 'sales_cost', setting: 'HIDE_MA_COST', permission: 'SHOW_MA_COST' },
    { name: 'gp', setting: 'HIDE_GP', permission: 'SHOW_GP' },
    { name: 'gp_percentage', setting: 'HIDE_GP_PERCENTAGE', permission: 'SHOW_GP_PERCENTAGE' },
    { name: 'landed_ma_amount', setting: 'HIDE_MA_COST', permission: 'SHOW_MA_COST' },
    { name: 'gp_landed', setting: 'HIDE_GP', permission: 'SHOW_GP' },
    { name: 'gp_percentage_landed', setting: 'HIDE_GP_PERCENTAGE', permission: 'SHOW_GP_PERCENTAGE' },
  ]

  rowData = [];
  totalRecords = 0;
  salesCost = 'cost_ma';
  optional = [];
  branchGuids = [];
  
  // api visa
  apiVisa = AppConfig.apiVisa;

  // initial grid state
  gridApi;
  gridColumnApi;

  gridOptions: GridOptions = {
    pagination: false,
    groupIncludeTotalFooter: true,
  };

  columnsDefs = [
    {
      headerName: 'Category 1',
      field: 'category1_code',
      rowGroup: false, hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Category 2',
      field: 'category2_code',
      rowGroup: false, hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Category 3',
      field: 'category3_code',
      rowGroup: false, hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Category 4',
      field: 'category4_code',
      rowGroup: false, hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Category 5',
      field: 'category5_code',
      rowGroup: false, hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Category 6',
      field: 'category6_code',
      rowGroup: false, hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Category 7',
      field: 'category7_code',
      rowGroup: false, hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Category 8',
      field: 'category8_code',
      rowGroup: false, hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Category 9',
      field: 'category9_code',
      rowGroup: false, hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Category 10',
      field: 'category10_code',
      rowGroup: false, hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Item Code',
      field: 'item_code',
      rowGroup: true, hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Item Name',
      field: 'item_name',
      type: 'textColumn'
    },
    {
      headerName: 'Type',
      field: 'type',
      type: 'textColumn'
    },
    {
      headerName: 'UOM',
      field: 'uom',
      type: 'textColumn'
    },
    {
      headerName: 'Branch',
      field: 'branch_code',
      type: 'textColumn'
    },
    {
      headerName: 'Location',
      field: 'location_code',
      rowGroup: false, hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Stock Balance',
      field: 'qty_balance',
      type: 'integerColumn'
    },
    {
      headerName: 'Qty Sold',
      field: 'qty_sold',
      type: 'integerColumn'
    },
    {
      headerName: 'Sales Amount',
      field: 'sales_amount',
      type: 'decimalColumn'
    },
    {
      headerName: 'Sales Cost',
      field: 'sales_cost',
      type: 'decimalColumn'
    },
    {
      headerName: 'Landed Cost',
      hide: true,
      field: 'landed_ma_amount',
      type: 'decimalColumn'
    },
    {
      headerName: 'GP (MA Cost)',
      field: 'gp',
      type: 'decimalColumn'
    },
    {
      headerName: 'GP% (MA Cost)',
      field: 'gp_percentage',
      type: 'decimalColumn',
      aggFunc: '',
      valueGetter: (params) => {
        const cost = this.calculateTotal(params.node, 'gp');
        const salesAmount = this.calculateTotal(params.node, 'sales_amount');

        if (cost && salesAmount && salesAmount !== 0) {
          return (cost / salesAmount) * 100;
        }
        return 0;
      },
    },
    {
      headerName: 'GP (MA Cost + Landed Cost)',
      hide: true,
      field: 'gp_landed',
      type: 'decimalColumn'
    },
    {
      headerName: 'GP% (MA Cost + Landed Cost)',
      hide: true,
      field: 'gp_percentage_landed',
      type: 'decimalColumn',
      aggFunc: '',
      valueGetter: (params) => {
        const cost = this.calculateTotal(params.node, 'gp_landed');
        const salesAmount = this.calculateTotal(params.node, 'sales_amount');

        if (cost && salesAmount && salesAmount !== 0) {
          return (cost / salesAmount) * 100;
        }
        return 0;
      },
    }
  ];

  readPermissionDefintion = {
    branch : 'API_TNT_DM_ERP_SALES_REPORT_BY_ITEM_CODE_READ'
  }

  constructor(
    protected viewColFacade: ViewColumnFacade,
    protected apiService: ApiService,
    protected toastr: ToastrService,
    private readonly permissionStore: Store<PermissionStates>,
    private readonly sessionStore: Store<SessionStates>,
    private readonly componentStore: ComponentStore<LocalState>) {
    super();
  }

  ngOnInit() {

    this.toggleColumn$ = this.viewColFacade.toggleColumn$;
    this.subs.sink = this.viewColFacade.rowData$.subscribe(rowData => this.rowData = rowData);
    this.subs.sink = this.viewColFacade.totalRecords$.subscribe(totalRecords => this.totalRecords = totalRecords);
    this.subs.sink = this.localState$.subscribe(a => {
      this.localState = a;
      this.componentStore.setState(a);
    });

    this.branchGuids = UtilitiesModule.getTargetsByPermission(this.userPermissionTarget$,
      this.readPermissionDefintion.branch, 'bl_fi_mst_branch'
    );
  }

  onNext() {
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState, deactivateAdd: true, deactivateList: false
    });
    this.viewColFacade.onNextAndReset(this.index, 1);
  }

  onGridReady(params) {
    console.log("grid ready sales report");
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.closeToolPanel();

    if (this.rowData.length > 0) {
      this.setDataRowCache();
    }
    else
    {
      //this.createData();
    }

    const statusBarComponent = this.gridApi.getStatusPanel('statusBarExportKey');
    statusBarComponent.setTitle(this.compName);
    statusBarComponent.setFilename(this.compName);
    statusBarComponent.setHideGroupColumn(true);
  }

  createData(inputModel?: SalesReportByItemCodeInputModel) {
    console.log("on create data....");
    this.clear();
    this.viewColFacade.loadInit(this.gridApi);
    this.subs.sink = this.apiService.getSalesReportByItemCode(inputModel, this.apiVisa).pipe(
      mergeMap(a => from(a.data).pipe(
        map(b => {
          const sales_cost = this.getBaseAmt(b, this.getSalesCost(b));
          const sales_amount = this.getBaseAmt(b, b.sales_amount);

          Object.assign(b,
            {
              sales_cost: sales_cost,
              sales_amount: sales_amount,
              gp: sales_amount - sales_cost,
              gp_percentage: Number(sales_amount) !== 0 ? (sales_amount - sales_cost) * 100 / b.sales_amount : 0,
              gp_landed: sales_amount - sales_cost - b.landed_cost_ma_amount,
              gp_percentage_landed: Number(sales_amount) !== 0 ? (sales_amount - sales_cost - b.landed_cost_ma_amount) * 100 / sales_amount : 0
            }
          )
          return b;
        }),
        toArray(),
        map(c => {
          a.data = c;
          return a;
        })
      ))
    ).subscribe(resolved => {
      console.log(resolved);
      this.viewColFacade.loadSuccess(resolved);
      this.rowData = this.checkDataRow(resolved.data);
      this.totalRecords = resolved.data.length;
      this.gridApi.setRowData(this.rowData);
      this.viewColFacade.selectRowData(this.rowData);
      this.viewColFacade.selectTotalRecords(this.totalRecords);
    }, err => {
      console.error(err);
      let msg = err.status===403 ? ErrorMessages.CRUD_403_ERRORS.READ : err.message;
      if (this.branchGuids.length === 0) {
        msg = 'Please select at least one branch to view the report.';
      }
      this.toastr.error(
        msg,
        "AG Gird Error",
        {
          tapToDismiss: true,
          progressBar: true,
          timeOut: 1300
        }
      );
      this.viewColFacade.loadFailed(err);
    });
  };

  onToggle(e: boolean) {
    this.viewColFacade.toggleColumn(e);
  }

  onRowClicked(entity: SalesReportByItemCodeModel) {
    if (entity) {
      console.log("on row clicked", entity.item_code);
    }
  }

  onSearch(e: SearchQueryModel) {
    console.log("search", e);
    if (!e.isEmpty) {
      if (e.keyword && e.keyword.length > 0 && e.keyword.length < 3)  {
        this.toastr.error(
          'Search keyword must more than 2 characters.',
          'Keyword',
          {
            tapToDismiss: true,
            progressBar: true,
            timeOut: 1300
          }
        );
        return;
      }

      const inputModel = {} as SalesReportByItemCodeInputModel;
      inputModel.keyword = e.keyword;
      inputModel.date_from = '2022-01-01T00:00:00.000Z';
      inputModel.date_to = '2099-12-31T00:00:00.000Z';
      inputModel.branch_guids = this.branchGuids ?? [];
      
      if (e.queryString) {
        inputModel.keyword = UtilitiesModule.checkNull(e.queryString['itemCode'],'');
        inputModel.branch_guids = UtilitiesModule.checkNull(e.queryString['branch'],[]);
        inputModel.date_from = UtilitiesModule.checkNull(e.queryString['date']['from'],'2022-01-01T00:00:00.000Z');
        inputModel.date_to = UtilitiesModule.checkNull(e.queryString['date']['to'],'2099-12-31T00:00:00.000Z');
        inputModel.customer_guids = UtilitiesModule.checkNull(e.queryString['customer'],[]);
        inputModel.salesman_guids = UtilitiesModule.checkNull(e.queryString['salesman'],[]);
        inputModel.item_type = UtilitiesModule.checkNull(e.queryString['itemType'],[]);
        this.optional = UtilitiesModule.checkNull(e.queryString['optional'],[]);

        const itemStatus = UtilitiesModule.checkNull(e.queryString['itemStatus'], []);
        inputModel.item_status = Array.isArray(itemStatus) ? itemStatus : [itemStatus];

        inputModel.item_category1_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel1'],[]);
        inputModel.item_category2_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel2'],[]);
        inputModel.item_category3_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel3'],[]);
        inputModel.item_category4_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel4'],[]);
        inputModel.item_category5_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel5'],[]);
        inputModel.item_category6_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel6'],[]);
        inputModel.item_category7_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel7'],[]);
        inputModel.item_category8_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel8'],[]);
        inputModel.item_category9_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel9'],[]);
        inputModel.item_category10_guids =UtilitiesModule.checkNull( e.queryString['itemCategoryLevel10'],[]);

        this.salesCost = UtilitiesModule.checkNull(e.queryString['calculateBaseOn'],'cost_ma');
      }
      inputModel.server_doc_type = ['INTERNAL_SALES_CASHBILL','INTERNAL_SALES_INVOICE','INTERNAL_SALES_RETURN'];
      this.createData(inputModel);

      const date_from = UtilitiesModule.dateFormatter(inputModel.date_from);
      const date_to = UtilitiesModule.dateFormatter(inputModel.date_to);
      // Set branch names asynchronously
      this.setBranchSubTitle(inputModel.branch_guids, date_from, date_to);  
    }
    else {
      //this.createData();
    }
  }

  clear() {
    this.gridApi.setRowData([]);
    this.totalRecords = 0;
    this.rowData = [];
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

  setDataRowCache() {
    //console.log('set data row cache');
    this.gridApi.setRowData(this.rowData);
  }

  getSalesCost(model: SalesReportByItemCodeModel) {
    return model[this.salesCost + '_amount'];
  }

  calculateTotal(node, key) {
    if (node.group) {
      let totalCost = 0;
      const children = node.childrenAfterGroup;

      children.forEach((childNode) => {
        const childCost = this.calculateTotal(childNode, key);
        totalCost += childCost;
      });

      return totalCost;
    } else {
      const rowData = node.data;
      return rowData && rowData[key] ? rowData[key] : 0;
    }
  }

  isHideGLCodeType() {
    return (this.optional.indexOf('HIDE_GL_CODE_TYPE') >= 0);
  }

  checkDataRow(rowData) {
    if (this.isHideGLCodeType()) {
      rowData = rowData.filter(item => item.type !== 'GL_CODE');
    }
    return rowData;
  }

  getBaseAmt(data, value) {
    if (!value || value === 0) return 0;

    if (this.isForex(data)) {
      return value / (data?.base_doc_xrate || 1);
    }
    else {
      return value;
    }
  }

  isForex(data): boolean {
    return data?.doc_ccy && data?.base_doc_ccy && data?.doc_ccy !== data?.base_doc_ccy && data?.base_doc_xrate > 0;
  }

  setBranchSubTitle(guids: string[], dateFrom: string, dateTo: string) {
    const key = 'branch';

    this.sessionStore.select(SessionSelectors.selectDropDownRowData).subscribe(resolved => {
      let rowData = resolved ? resolved[key] ? resolved[key] : [] : [];
      let branchText = "ALL BRANCHES";

      if (rowData.length > 0 && guids && guids.length > 0) {
        const branches = rowData.filter(row => guids.includes(row.guid));
        if (branches.length > 0) {
          branchText = branches.map(branch => `${branch.name} (${branch.code})`).join(', ');
        }
      }

      let subTitle = branchText;
      subTitle += "\n";
      subTitle += `${dateFrom} to ${dateTo}`;

      const statusBarComponent = this.gridApi.getStatusPanel('statusBarExportKey');
      statusBarComponent.setSubTitle(subTitle);
    });
  }
  
}
