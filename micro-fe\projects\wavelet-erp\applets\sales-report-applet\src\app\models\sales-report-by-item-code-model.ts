export interface SalesReportByItemCodeModel {
    branch_code: string;
    branch_name: string;
    location_code: string;
    location_name: string;
    fi_item_guid: string;
    qty_balance: number;
    qty_sold: number;
    sales_amount: number;
    item_code: string;
    item_name: string;
    item_descr: string;
    type: string;
    uom: string;
    sub_type: string;
    cost_ma_amount: number;
    cost_wa_amount: number;
    cost_fifo_amount: number;
    cost_lifo_amount: number;
    cost_manual_amount: number;
    cost_replacement_amount: number;
    cost_last_purchase_location: number;
    cost_last_purchase_company: number;
    landed_cost_ma_amount: number;
    landed_cost_wa_amount: number;
    landed_cost_fifo_amount: number;
    landed_cost_lifo_amount: number;
    category0_code: string;
    category0_name: string;
    category1_code: string;
    category1_name: string;
    category2_code: string;
    category2_name: string;
    category3_code: string;
    category3_name: string;
    category4_code: string;
    category4_name: string;
    category5_code: string;
    category5_name: string;
    category6_code: string;
    category6_name: string;
    category7_code: string;
    category7_name: string;
    category8_code: string;
    category8_name: string;
    category9_code: string;
    category9_name: string;
    category10_code: string;
    category10_name: string;
    hour: string;
}

export interface SalesReportByItemCodeInputModel {
    branch_guids: any[];
    keyword: string;
    date_from: string;
    date_to: string;
    report_period: string;
    customer_guids: any[];
    salesman_guids: any[];
    item_type: any[];
    item_status: any[];

    item_category1_guids: any[];
    item_category2_guids: any[];
    item_category3_guids: any[];
    item_category4_guids: any[];
    item_category5_guids: any[];
    item_category6_guids: any[];
    item_category7_guids: any[];
    item_category8_guids: any[];
    item_category9_guids: any[];
    item_category10_guids: any[];

    server_doc_type: any[];
    customer_category_guids: any[];
    group_by: any[];
}

export interface MultiBranchDailySalesPurchaseInputDto {
  branch_guids: any[];
  keyword: string;
  date_from: string;
  date_to: string;
  customer_category_guids: any[];
  exclude_glcode_guids: any[];
  date_option: string;
  item_type: any[];
}

export interface CollectionInputDto {
  branch_guids: any[];
  keyword: string;
  date_from: string;
  date_to: string;
  customer_category_guids: any[];
}

export interface CollectionCreditCardInputDto {
  branch_guids: any[];
  keyword: string;
  date_from: string;
  date_to: string;
  salesman_guids: any[];
  cashbook_guids: any[];
  customer_category_guids: any[];
  date_option: string;
  credit_card_guids: any[];
}
