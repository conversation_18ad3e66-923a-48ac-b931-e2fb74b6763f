import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { FlexLayoutModule } from "@angular/flex-layout";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { NgxMatSelectSearchModule } from "ngx-mat-select-search";
import { PerfectScrollbarModule } from "ngx-perfect-scrollbar";
import { MaterialModule } from "../modules/material.module";
import { AdvancedSearchComponent } from "./advanced-search/advanced-search.component";
import { AppletLogoComponent } from "./applet-logo/applet-logo.component";
import { BreadcrumbComponent } from "./breadcrumb/breadcrumb.component";
import { ColumnToggleComponent } from "./column-toggle/column-toggle.component";
import { CreditTermsComponent } from "./credit-terms/credit-terms.component";
import { CurrencyComponent } from "./currency/currency.component";
import { DragDropDirective } from "./drag-drop.directive";
import { FirstColumnDirective } from "./first-column.directive";
import { FourOhFourComponent } from "./four-oh-four/four-oh-four.component";
import { GridToggleComponent } from "./grid-toggle/grid-toggle.component";
import { PaginationComponent } from "./pagination/pagination.component";
import { PermissionDirective } from "./permission.directive";
import { PricingSchemeUOMComponent } from "./pricing-scheme-uom/pricing-scheme-uom.component";
import { SalesAgentComponent } from "./sales-agent/sales-agent.component";
import { SalesAgentSearchComponent } from "./sales-agent-v2/sales-agent.component";
import { SalesManComponent } from "./sales-man/sales-man.component";
import { SecondColumnDirective } from "./second-column.directive";
import { SelectSettlementMethodComponent } from "./select-settlement-method/select-settlement-method.component";
import { SSTComponent } from "./sst/sst.component";
import { WHTComponent } from "./wht/wht.component";
import { SelectSetOfBooksDropDownComponent } from "./select-set-of-books-drop-down/select-set-of-books-drop-down.component";
import * as moment from "moment";
import { AdvancedSearchLinesComponent } from "./advanced-search-lines/advanced-search-lines.component";
import { LineContainerKoByComponent } from "./line-container-ko-by/line-container-ko-by.component";
import { LineContainerKoForComponent } from "./line-container-ko-for/line-container-ko-for.component";
import { LineContainerKoForQueueComponent } from "./line-container-ko-for-queue/line-container-ko-for-queue.component";
import { AgGridModule } from "ag-grid-angular";
import { DocLinkKoForComponent } from "./doc-link-ko-for/doc-link-ko-for.component";
import { DocLinkKoByComponent } from "./doc-link-ko-by/doc-link-ko-by.component";
import { UOMComponent } from "./uom/uom.component";
import { SelectMultiLocationDropDownComponent } from "./select-multi-location-drop-down/select-multi-location-drop-down.component";
import { SelectMultiLocationDropDownStockReportComponent } from "./select-multi-location-drop-down-stock-report/select-multi-location-drop-down-stock-report.component"
import { PaginationClientSideComponent } from "./pagination-client-side/pagination-client-side.component";
import { SelectGlcodeDropDownComponent } from "./select-glcode-drop-down/select-glcode-drop-down.component";
import { GenericDocumentPostingComponent } from "./generic-document-posting/generic-document-posting.component";
import { JournalTxnComponent } from "./generic-document-posting/journal-txn/journal-txn.component";
import { CashbookTxnComponent } from "./generic-document-posting/cashbook-txn/cashbook-txn.component";
import { TaxTxnComponent } from "./generic-document-posting/tax-txn/tax-txn.component";
import { PointsTxnComponent } from "./generic-document-posting/points-txn/points-txn.component";
import { InvTxnComponent } from "./generic-document-posting/inv-txn/inv-txn.component";
import { PermissionHideDirective } from "./permissionHide.directive";
import { SelectSubledgerComponent } from "./select-subledger/select-subledger.component";
import { BranchComponent } from "./branch/branch.component";
import { AdvancedSearchV2Component } from "./advanced-search-v2/advanced-search-v2.component";
import { LocationFromComponent } from "./location-from/location-from.component";
import { LocationToComponent } from "./location-to/location-to.component";
import { AppletComponent } from "./applet/applet.component";
import { WorkflowProcessComponent } from "./workflow-process/workflow-process.component";
import { MAT_SNACK_BAR_DEFAULT_OPTIONS } from "@angular/material/snack-bar";
import { SelectMultiBranchDropDownComponent } from "./select-multi-branch-drop-down/select-multi-branch-drop-down.component";
import { PaginationClientSideV2Component } from "./pagination-client-side-v2/pagination-client-side-v2.component";
import { SelectPrintableFormatAppletLoginEpComponent } from "./select-printable-format-applet-login-ep/select-printable-format-applet-login-ep.component";
import { SalesManAppletLoginComponent } from "./sales-man-applet-login/sales-man-applet-login.component";
import { SelectTariffCodeComponent } from "./select-tariff-code/select-tariff-code.component";
import { UtilItemSerialNumberComponent } from "./serial-number/serial-number.component";
import { SerialNumberScanComponent } from "./serial-number/serial-number-scan/serial-number-scan.component";
import { SerialNumberListingComponent } from "./serial-number/serial-number-listing/serial-number-listing.component";
import { SerialNumberImportComponent } from "./serial-number/serial-number-import/serial-number-import.component";
import { CompanyComponent } from "./company/company.component";
import { CompanyV2 } from "./company-v2/company-v2.component";
import { PricingSchemeComponent } from "./pricing-scheme/pricing-scheme.component";
import { PersonInChargeComponent } from "./person-in-charge/person-in-charge.component";
import { SelectPrintableFormatComponent } from "./select-printable-format/select-printable-format.component";
import { TimestampTzDatatypeInterface, bl_fi_generic_doc_line_RowInterface } from "blg-akaun-ts-lib";
import { UtilLocationDropdownComponent } from "./util-location-dropdown/util-location-dropdown.component";
import { LocationDropdownStockComponent } from "./location-dropdown-stock/location-dropdown-stock.component";
import { PaginationV2Component } from "./pagination-v2/pagination-v2.component";
import { CommissionCycleDropDownComponent } from "./commission-cycle-dropdown/commission-cycle-dropdown.component";
import { SelectMultiCompanyDropDownComponent } from "./select-multi-company-drop-down/select-multi-company-drop-down.component";
import { SelectMultiEntityDropDownComponent } from "./select-multi-entity-drop-down/select-multi-entity-drop-down.component";
import { AdvancedSearchGeneralComponent } from "./advanced-search-general/advanced-search-general.component";
import { SelectMultiLabelListDropDownComponent } from "./select-multi-label-list-drop-down/select-multi-label-list-drop-down.component";
import { SelectMultiItemCategoryLevelDropDownComponent } from "./select-multi-item-category-level-drop-down/select-multi-item-category-level-drop-down.component";
import { RecurrenceEditorComponentComponent } from './recurrence-editor-component/recurrence-editor-component.component';
import { EInvoiceRecurrenceEditorComponentComponent } from './e-invoice-recurrence-editor-component/e-invoice-recurrence-editor-component.component';
import { ExportStatusBarComponent } from "./status-bar/export-status-bar.component";
import { PaginationServerSideV2Component } from './pagination-server-side-v2/pagination-server-side-v2.component';
import { AdvancedSearchEntitiesComponent } from "./advanced-search-entities/advanced-search-entities.component";
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from "@angular/material/core";
import { MAT_MOMENT_DATE_ADAPTER_OPTIONS, MomentDateAdapter } from "@angular/material-moment-adapter";
import { ExportButtonComponent } from "./export-button/export-button.component";
import { MonthYearPickerComponent } from './month-year-picker/month-year-picker.component';
import { WeekPickerComponent } from './week-picker/week-picker.component';
import { ParentPrintableDropdownComponent } from './parent-printable-dropdown/parent-printable-dropdown.component';
import { SelectEmailTemplateComponent } from "./select-email-template/select-email-template.component";
// import { SelectVehiclesDropdownComponent } from './select-vehicles-drop-down/select-vehicles-drop-down.component';
import { AdvancedSearchCashbookComponent } from "./advanced-search-cashbook/advanced-search-cashbook.component";
import { SelectGLCodeV2Component } from "./select-gl-code-v2/select-gl-code-v2.component";
import { NumericAGCellEditor } from "./numeric-ag-cell-editor/numeric-ag-cell-editor.component";
import { WholeNumberAGCellEditor } from "./numeric-ag-cell-editor/whole-number-ag-cell-editor.component";
import { SelectMultiCustomerDropDownComponent } from './select-multi-customer-drop-down/select-multi-customer-drop-down.component'
import { SelectMultiCustomerCategoryDropDownComponent } from './select-multi-customer-category-drop-down/select-multi-customer-category-drop-down.component'
import { WarehouseAdvancedSearchComponent } from "./warehouse-advanced-search-drop-down/warehouse-advanced-search-drop-down.component"
import { SelectEmployeeDropDownComponent } from "./select-employee-drop-down/select-employee-drop-down.component";
import { SelectWarehouseDropDownComponent } from "./select-warehouse-drop-down/select-warehouse-drop-down.component";
import { CompanyWorkflowDropdownComponent } from "./company-workflow-dropdown/company-workflow-dropdown.component";
import { PricingSchemeV2Component } from "./pricing-scheme-v2/pricing-scheme-v2.component";
import { RefreshComponent } from "./refresh/refresh.component";
import { UOMV2Component } from "./uom-v2/uom-v2.component";
import { RequireDeliveryCellRendererComponent } from "./require-delivery-cell-renderer/require-delivery-cell-renderer.component";
import { DeliveryTypeCellRendererComponent } from "./delivery-type-cell-renderer/delivery-type-cell-renderer.component";
import { SelectMultiCurrencyDropDownComponent } from "./select-multi-currency-drop-down/select-multi-currency-drop-down.component";
import { UomWmsItemComponent } from "./uom-wms-item/uom-wms-item.component"
import { CustomTooltipComponent } from './custom-tooltip/custom-tooltip.component';
import { CustomLoadingOverlayComponent } from './custom-loading-overlay/custom-loading-overlay.component';
import { PaginationClientSideV3Component } from './pagination-client-side-v3/pagination-client-side-v3.component';
import { AdvancedSearchWmsItemsComponent } from "./advanced-search-wms-items/advanced-search-wms-items.component";
import { SelectIntercompanyBranchDownComponent } from "./select-intercompany-branch-drop-down/select-intercompany-branch-drop-down.component";
import { PurchaserComponent } from "./purchaser/purchaser.component";
import { CashbookAdvancedSearchDropdownComponent } from "./advanced-search-cashbook-dropdown/advanced-search-cashbook-dropdown.component";
import { SelectGLCategryDropDownComponent } from './select-glcategory-drop-down/select-glcategory-drop-down.component';
import { SerialNumberOptionalComponent } from './serial-number-optional/serial-number-optional.component';
import { SerialNumberImportOptionalComponent } from './serial-number-optional/serial-number-import-optional/serial-number-import-optional.component';
import { SerialNumberScanOptionalComponent } from './serial-number-optional/serial-number-scan-optional/serial-number-scan-optional.component';
import { SelectMultiGLCodeDropDownComponent } from "./select-multi-glcode-drop-down/select-multi-glcode-drop-down.component";
import { SelectMultiDeviceDropDownComponent } from "./select-multi-device-drop-down/select-multi-device-drop-down.component";
import { SelectMultiChartOfAccountDropDownComponent } from "./select-multi-chart-of-account-drop-down/select-multi-chart-of-account-drop-down.component";
import { SelectMultiGLCategoryDropDownComponent } from "./select-multi-glcategory-drop-down/select-multi-glcategory-drop-down.component";
import { ButtonRendererComponent } from "./button-renderer/button-renderer.component";
import { AkaunConfirmationDialogComponent } from "./dialogues/akaun-confirmation-dialog/akaun-confirmation-dialog";
import { AkaunMessageDialogComponent } from "./dialogues/akaun-message-dialog/akaun-message-dialog";
import { WorkflowActionComponent } from "./workflow-action/workflow-action.component";
import { WorkflowActionTypeComponent } from "./workflow-action-type/workflow-action-type.component";
import { WmsContainerAdvancedSearchDropdownComponent } from "./advanced-search-wms-container-dropdown/advanced-search-wms-container-dropdown.component";
import { WmsLayoutNodeAdvancedSearchDropdownComponent } from "./advanced-search-wms-layout-node-dropdown/advanced-search-wms-layout-node-dropdown.component";
import { TimezoneDropdownComponent } from "./timezone-dropdown/timezone-dropdown.component";
import { PrintablePageComponent } from "./printable-page/printable-page.component";
import { SelectPricebookComponent } from "./pricebook/pricebook.component";
import { SelectMultiGLSectionDropDownComponent } from "./select-multi-glsection-drop-down/select-multi-glsection-drop-down.component";
import { SelectLocationDropDownLatestComponent } from "./blg-select-location-drop-down-latest/blg-select-location-drop-down-latest.component";
import { SelectNSTIDropDownComponent } from "./select-nsti-drop-down/select-nsti-drop-down.component";
import { SelectPrintableFormatWithFilterComponent } from './select-printable-format-with-filter/select-printable-format-with-filter/select-printable-format-with-filter.component';
import { SelectBudgetItemComponent } from './select-budget-item/select-budget-item.component';
import { SelectBudgetRegisterComponent } from './select-budget-register/select-budget-register.component';
import { SelectBudgetFiscalYearComponent } from './select-budget-fiscal-year/select-budget-fiscal-year.component';
import { SelectBudgetVotebookGenDocComponent } from './select-budget-votebook-gen-doc/select-budget-votebook-gen-doc.component';
import { SelectBudgetRegisterGenDocComponent } from './select-budget-register-gen-doc/select-budget-register-gen-doc.component';
import { SelectBudgetFiscalPeriodGenDocComponent } from './select-budget-fiscal-period-gen-doc/select-budget-fiscal-period-gen-doc.component';
import { SelectBudgetItemGenDocComponent } from './select-budget-item-gen-doc/select-budget-item-gen-doc.component';
import { SelectBudgetFiscalYearGenDocComponent } from './select-budget-fiscal-year-gen-doc/select-budget-fiscal-year-gen-doc.component';
import { AdvancedSearchClientSideComponent } from "./advanced-search-client-side/advanced-search-client-side.component";
import { AkaunDiscardDialogComponent } from "../dialogues/akaun-discard-dialog/akaun-discard-dialog.component";
import { EinvoiceUOMComponent } from "../utilities/einvoice-uom/einvoice-uom.component"
import { SelectMultiEmployeeDropDownComponent } from "./select-multi-employee-drop-down/select-multi-employee-drop-down.component";
import { SelectMultiEmployeeDropDownV2Component } from "./select-multi-employee-drop-down-v2/select-multi-employee-drop-down-v2.component";
import { AkaunVoidDialogComponent } from "../dialogues/akaun-void-dialog/akaun-void-dialog.component";
import { SelectMultiDropDownComponent } from "./select-multi-drop-down/select-multi-drop-down.component";
import { SelectMultiFiItemDropDownComponent } from "./select-multi-fi-item-drop-down/select-multi-fi-item-drop-down.component";
import { CloseConfirmDialogComponent } from './close-confirmation-popup/close-confirm-dialog.component';
import { RejectionReasonPopUpComponent } from "./einvoice-request-rejection-reason-popup/rejection-popup.component";
import { DefaultLanguageSelectDropDownComponent } from "./default-language-select-drop-down/default-language-select-drop-down.component";
import { SelectMultiPricingSchemeDropDownComponent } from "./select-multi-pricing-scheme-drop-down/select-multi-pricing-scheme-drop-down.component";
import { SelectMultiLocationLabelDropDownComponent } from "./select-multi-location-label-drop-down/select-multi-location-label-drop-down.component";
import { SelectCustomerDropdownComponent } from './select-customer-dropdown/select-customer-dropdown.component';
import { ForexDataSourceComponent } from "./forex-data-source-drop-down/forex-data-source-drop-down.component";
import { RoleDropdownComponent } from './role-dropdown/role-dropdown.component';
import { ConsulationNoDropdownComponent } from './consultation-no-dropdown/consultation-no-dropdown.component';
import { ConsulationNameDropdownComponent } from './consultation-name-dropdown/consultation-name-dropdown.component';
import { VehicleNoDropdownComponent } from "./vehicle-no-dropdown/vehicle-no-dropdown.component";
import { VehicleBrandNameDropdownComponent } from "./vehicle-brand-name-dropdown/vehicle-brand-name-dropdown.component";
import { VehicleCapacityDropdownComponent } from "./vehicle-capacity-dropdown/vehicle-capacity-dropdown.component";
import { VehicleModelYearDropdownComponent } from "./vehicle-model-year-dropdown/vehicle-model-year-dropdown.component";
import { AdvancedSearchLinesV2Component } from './advanced-search-lines-v2/advanced-search-lines-v2.component';
import { SelectMultiItemCategoryDropDownComponent } from './select-multi-item-category-drop-down/select-multi-item-category-drop-down.component';
import { SelectMultiCashbookDropDownComponent } from "./select-multi-cashbook-drop-down/select-multi-cashbook-drop-down.component";
import { EscapeHtmlPipe } from "./Pipe/escape-html.pipe";
import { SelectItemGlCodeComponent } from './select-item-gl-code/select-item-gl-code.component';
import { SelectMultiTaxCodeDropDownComponent } from "./select-multi-tax-code-drop-down/select-multi-tax-code-drop-down.component";
import { AdvancedSearchEInvoiceComponent } from "./advanced-search-einvoice/advanced-search-einvoice.component";
import { AdvancedSearchEInvoicePortalComponent } from "./advanced-search-einvoice-portal/advanced-search-einvoice-portal.component";
import { BranchDropdownSimpleComponent } from "./branch-dropdown-simple/branch-dropdown-simple.component";
import { CustomerDropdownSimpleComponent } from "./customer-dropdown-simple/customer-dropdown-simple.component";
import { ExportDataDialogComponent } from "./export-data-dialog/export-data-dialog.component";
import { SelectSupplierDropdownComponent } from './select-supplier-dropdown/select-supplier-dropdown.component';
import { SelectMultiLocationDropdownComponent } from './select-multi-location-dropdown/select-multi-location-dropdown.component';
import { PaginationV2EntityComponent } from "./pagination-v2-entity/pagination-v2-entity.component";
import { AkaunGenDocLockDialogComponent } from "../dialogues/akaun-gen-doc-lock-dialog/akaun-gen-doc-lock-dialog.component";
import { MultiLevelDiscountComponent } from './multi-level-discount/multi-level-discount.component';
import { GenericDocumentViewMainComponent } from "./generic-document-view-main/generic-document-view-main.component";
import { GenericDocumentViewLineComponent } from "./generic-document-view-line/generic-document-view-line.component";
import { GenericDocumentViewSettlementComponent } from "./generic-document-view-settlement/generic-document-view-settlement.component";
import { GenericDocumentViewExportComponent } from "./generic-document-view-export/generic-document-view-export.component";
import { CashDocumentViewMainComponent } from "./cash-document-view-main/cash-document-view-main.component";
import { CashDocumentViewLineComponent } from "./cash-document-view-line/cash-document-view-line.component";
import { CashDocumentPostingComponent } from "./cash-document-posting/cash-document-posting.component";
import { CashDocumentPostingJournalTxnComponent } from "./cash-document-posting/journal-txn/journal-txn.component";
import { CashDocumentPostingCashbookTxnComponent } from "./cash-document-posting/cashbook-txn/cashbook-txn.component";
import { CashDocumentViewExportComponent } from "./cash-document-view-export/cash-document-view-export.component";
import { SupplierDropdownSimpleComponent } from "./supplier-dropdown-simple/supplier-dropdown-simple.component";
import { GenericDocumentViewComponent } from "./generic-document-view/generic-document-view.component";
import { CellClickHandlerComponent } from './cell-click-handler.component';
import { StoreModule } from '@ngrx/store';
import { columnViewModelFeatureKey, columnViewModelReducers } from '../generic-doc-view-model-controller/generic-doc-cashbook-view-model-controller/reducers';
import { columnViewModelFeatureKey1, columnViewModelReducers1 } from '../generic-doc-view-model-controller/generic-doc-journal-view-model-controller/reducers';
import { columnViewModelFeatureKey2, columnViewModelReducers2 } from '../generic-doc-view-model-controller/generic-doc-points-view-model-controller/reducers';
import { columnViewModelFeatureKey3, columnViewModelReducers3 } from '../generic-doc-view-model-controller/generic-doc-tax-view-model-controller/reducers';
import { columnViewModelFeatureKey4, columnViewModelReducers4 } from '../generic-doc-view-model-controller/generic-doc-inv-view-model-controller/reducers';
import { SelectRoundingItemComponent } from './select-rounding-item/select-rounding-item.component';
import { SelectGroupDiscountItemComponent } from './select-group-discount-item/select-group-discount-item.component';
import { SelectDefaultSettlementItemComponent } from './select-default-settlement-method/select-default-settlement-method.component';
import { UtilPricingSchemeHdrComponent } from './pricing-scheme-hdr/pricing-scheme-hdr.component';
import { SelectPosCardTerminalDropDownComponent } from './select-pos-card-terminal-drop-down/select-pos-card-terminal-drop-down.component';
import { CurrencyRateComponent } from './currency-rate/currency-rate.component';
import { AdvancedSearchPermSetComponent } from './advanced-search-perm-set/advanced-search-perm-set.component';
import { SelectMultiCompanyDropDownEinvoiceComponent } from "./select-multi-company-drop-down-einvoice/select-multi-company-drop-down-einvoice.component";
import { SelectBudgetVotebookComponent } from './select-budget-votebook/select-budget-votebook.component';
import { VotebookDropdownSimpleComponent } from './votebook-dropdown-simple/votebook-dropdown-simple.component';
import { FiscalYearDropdownSimpleComponent } from './fiscal-year-dropdown-simple/fiscal-year-dropdown-simple.component';
import { BudgetLineDropdownSimpleComponent } from './budget-line-dropdown-simple/budget-line-dropdown-simple.component';
import { SelectMultiVehicleDropDownComponent } from "./select-multi-vehicle-drop-down/select-multi-vehicle-drop-down.component";
import { AkaunGenericDocViewDialogComponent} from '../dialogues/akaun-generic-doc-view-dialog/akaun-generic-doc-view-dialog.component';
import { SelectMultiEntityDropDownComponentV2 } from "./select-multi-entity-drop-down-v2/select-multi-entity-drop-down.component";
import {InputTextCellRendererComponent} from "./input-text-cell-renderer/input-text-cell-renderer.component";
import { DocLinkViewComponent } from './doc-link-view/doc-link-view.component';
import { DocLinkViewCopyToComponent } from './doc-link-view/doc-link-view-copy-to/doc-link-view-copy-to.component';
import { DocLinkViewCopyFromComponent } from './doc-link-view/doc-link-view-copy-from/doc-link-view-copy-from.component';
import { ContraViewListingComponent } from './contra-view-listing/contra-view-listing.component';
import { AttachmentViewComponent } from './attachment-view/attachment-view.component';
import { ArapViewComponent } from './arap-view/arap-view.component';
import { AdvancedSearchOpqComponent } from './advanced-search-opq/advanced-search-opq.component';
import { SelectMultiTaxFilingDropDownComponent } from './select-multi-tax-filing-drop-down/select-multi-tax-filing-drop-down.component';
import { MultiMemberSelectionDropDownComponent } from "./select-multi-member-drop-down/select-multi-member-drop-down.component";
import { CommonEntityDropDownComponent } from "./select-entity-drop-down/select-entity-drop-down.component";
import { PermittedUsersByClientSidePermDropdownComponent } from './permitted-users-by-clientside-perm-dropdown/permitted-users-dropdown.component';
import { AgGridCustomComponent } from './ag-grid-custom/ag-grid-custom.component';
import { StateDropDownStatusBarComponent } from './status-bar/state-dropdown-status-bar.component';
import { SelectMultiInvItemDropDownComponent } from './select-multi-inv-item-drop-down/select-multi-inv-item-drop-down.component';
import { Observable } from 'rxjs';
import { take } from 'rxjs/operators';
import { PaginationStatusBarComponent } from './status-bar/pagination-status-bar.component';
import { AkaunPinNumberDialogComponent } from "../dialogues/akaun-pin-number-dialog/akaun-pin-number-dialog.component";

const MY_FORMATS = {
  parse: {
    dateInput: 'LL',
  },
  display: {
    dateInput: 'YYYY-MM-DD',
    monthYearLabel: 'YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'YYYY',
  },
};

@NgModule({
  declarations: [
    AdvancedSearchComponent,
    PaginationComponent,
    AppletLogoComponent,
    ColumnToggleComponent,
    GridToggleComponent,
    BreadcrumbComponent,
    FourOhFourComponent,
    CurrencyComponent,
    ForexDataSourceComponent,
    CreditTermsComponent,
    SelectSettlementMethodComponent,
    SalesAgentSearchComponent,
    SalesAgentComponent,
    SalesManComponent,
    SSTComponent,
    WHTComponent,
    DragDropDirective,
    FirstColumnDirective,
    SecondColumnDirective,
    PermissionDirective,
    PricingSchemeUOMComponent,
    SelectSetOfBooksDropDownComponent,
    AdvancedSearchLinesComponent,
    LineContainerKoByComponent,
    LineContainerKoForComponent,
    LineContainerKoForQueueComponent,
    DocLinkKoForComponent,
    DocLinkKoByComponent,
    UOMComponent,
    SelectMultiLocationDropDownComponent,
    SelectMultiLocationDropDownStockReportComponent,
    PaginationClientSideComponent,
    SelectGlcodeDropDownComponent,
    GenericDocumentPostingComponent,
    JournalTxnComponent,
    CashbookTxnComponent,
    TaxTxnComponent,
    PointsTxnComponent,
    InvTxnComponent,
    PermissionHideDirective,
    SelectSubledgerComponent,
    BranchComponent,
    // SelectVehiclesDropdownComponent,
    AdvancedSearchV2Component,
    LocationFromComponent,
    LocationToComponent,
    AppletComponent,
    SelectMultiBranchDropDownComponent,
    WorkflowProcessComponent,
    WorkflowActionComponent,
    WorkflowActionTypeComponent,
    PaginationClientSideV2Component,
    SelectPrintableFormatAppletLoginEpComponent,
    SalesManAppletLoginComponent,
    SelectTariffCodeComponent,
    UtilItemSerialNumberComponent,
    SerialNumberScanComponent,
    SerialNumberListingComponent,
    SerialNumberImportComponent,
    CompanyComponent,
    CompanyV2,
    PricingSchemeComponent,
    PersonInChargeComponent,
    SelectPrintableFormatComponent,
    UtilLocationDropdownComponent,
    LocationDropdownStockComponent,
    PaginationV2Component,
    CommissionCycleDropDownComponent,
    SelectMultiCompanyDropDownComponent,
    SelectMultiEntityDropDownComponent,
    PaginationServerSideV2Component,
    AdvancedSearchEntitiesComponent,
    AdvancedSearchGeneralComponent,
    SelectMultiLabelListDropDownComponent,
    SelectMultiItemCategoryLevelDropDownComponent,
    RecurrenceEditorComponentComponent,
    EInvoiceRecurrenceEditorComponentComponent,
    ExportStatusBarComponent,
    ExportButtonComponent,
    MonthYearPickerComponent,
    WeekPickerComponent,
    ParentPrintableDropdownComponent,
    SelectEmailTemplateComponent,
    AdvancedSearchCashbookComponent,
    SelectGLCodeV2Component,
    NumericAGCellEditor,
    WholeNumberAGCellEditor,
    SelectMultiCustomerDropDownComponent,
    SelectMultiCustomerCategoryDropDownComponent,
    WarehouseAdvancedSearchComponent,
    SelectEmployeeDropDownComponent,
    SelectWarehouseDropDownComponent,
    CompanyWorkflowDropdownComponent,
    PricingSchemeV2Component,
    RefreshComponent,
    UOMV2Component,
    RequireDeliveryCellRendererComponent,
    DeliveryTypeCellRendererComponent,
    SelectMultiCurrencyDropDownComponent,
    UomWmsItemComponent,
    CustomTooltipComponent,
    CustomLoadingOverlayComponent,
    PaginationClientSideV3Component,
    AdvancedSearchWmsItemsComponent,
    SelectIntercompanyBranchDownComponent,
    PurchaserComponent,
    CashbookAdvancedSearchDropdownComponent,
    SelectGLCategryDropDownComponent,
    SerialNumberOptionalComponent,
    SerialNumberImportOptionalComponent,
    SerialNumberScanOptionalComponent,
    SelectMultiGLCodeDropDownComponent,
    SelectMultiDeviceDropDownComponent,
    SelectMultiChartOfAccountDropDownComponent,
    SelectMultiGLCategoryDropDownComponent,
    ButtonRendererComponent,
    AkaunConfirmationDialogComponent,
    AkaunMessageDialogComponent,
    WmsContainerAdvancedSearchDropdownComponent,
    WmsLayoutNodeAdvancedSearchDropdownComponent,
    TimezoneDropdownComponent,
    PrintablePageComponent,
    SelectPricebookComponent,
    SelectMultiGLSectionDropDownComponent,
    SelectLocationDropDownLatestComponent,
    SelectNSTIDropDownComponent,
    SelectPrintableFormatWithFilterComponent,
    SelectBudgetItemComponent,
    SelectBudgetRegisterComponent,
    SelectBudgetFiscalYearComponent,
    SelectBudgetVotebookGenDocComponent,
    SelectBudgetRegisterGenDocComponent,
    SelectBudgetFiscalPeriodGenDocComponent,
    SelectBudgetItemGenDocComponent,
    SelectBudgetFiscalYearGenDocComponent,
    AdvancedSearchClientSideComponent,
    DefaultLanguageSelectDropDownComponent,
    AkaunDiscardDialogComponent,
    EinvoiceUOMComponent,
    SelectMultiEmployeeDropDownComponent,
    AkaunVoidDialogComponent,
    SelectMultiDropDownComponent,
    SelectMultiFiItemDropDownComponent,
    CloseConfirmDialogComponent,
    RejectionReasonPopUpComponent,
    SelectMultiPricingSchemeDropDownComponent,
    SelectMultiPricingSchemeDropDownComponent,
    SelectMultiLocationLabelDropDownComponent,
    SelectMultiPricingSchemeDropDownComponent,
    SelectCustomerDropdownComponent,
    RoleDropdownComponent,
    AdvancedSearchLinesV2Component,
    SelectMultiItemCategoryDropDownComponent,
    ConsulationNoDropdownComponent,
    ConsulationNameDropdownComponent,
    VehicleNoDropdownComponent,
    VehicleBrandNameDropdownComponent,
    VehicleCapacityDropdownComponent,
    VehicleModelYearDropdownComponent,
    SelectMultiCashbookDropDownComponent,
    EscapeHtmlPipe,
    SelectItemGlCodeComponent,
    SelectMultiTaxCodeDropDownComponent,
    AdvancedSearchEInvoiceComponent,
    AdvancedSearchEInvoicePortalComponent,
    BranchDropdownSimpleComponent,
    CustomerDropdownSimpleComponent,
    ExportDataDialogComponent,
    SelectSupplierDropdownComponent,
    SelectMultiLocationDropdownComponent,
    PaginationV2EntityComponent,
    AkaunGenDocLockDialogComponent,
    MultiLevelDiscountComponent,
    GenericDocumentViewMainComponent,
    GenericDocumentViewLineComponent,
    GenericDocumentViewSettlementComponent,
    GenericDocumentViewExportComponent,
    CashDocumentViewMainComponent,
    CashDocumentViewLineComponent,
    CashDocumentPostingComponent,
    CashDocumentPostingJournalTxnComponent,
    CashDocumentPostingCashbookTxnComponent,
    CashDocumentViewExportComponent,
    SupplierDropdownSimpleComponent,
    CellClickHandlerComponent,
    GenericDocumentViewComponent,
    SelectRoundingItemComponent,
    SelectGroupDiscountItemComponent,
    SelectDefaultSettlementItemComponent,
    UtilPricingSchemeHdrComponent,
    SelectPosCardTerminalDropDownComponent,
    CurrencyRateComponent,
    SelectMultiEmployeeDropDownV2Component,
    AdvancedSearchPermSetComponent,
    SelectMultiCompanyDropDownEinvoiceComponent,
    SelectBudgetVotebookComponent,
    VotebookDropdownSimpleComponent,
    FiscalYearDropdownSimpleComponent,
    BudgetLineDropdownSimpleComponent,
    SelectMultiVehicleDropDownComponent,
    AkaunGenericDocViewDialogComponent,
    SelectMultiEntityDropDownComponentV2,
    InputTextCellRendererComponent,
    DocLinkViewComponent,
    DocLinkViewCopyToComponent,
    DocLinkViewCopyFromComponent,
    ContraViewListingComponent,
    AttachmentViewComponent,
    ArapViewComponent,
    AdvancedSearchOpqComponent,
    SelectMultiTaxFilingDropDownComponent,
    MultiMemberSelectionDropDownComponent,
    CommonEntityDropDownComponent,
    PermittedUsersByClientSidePermDropdownComponent,
    AgGridCustomComponent,
    StateDropDownStatusBarComponent,
    SelectMultiInvItemDropDownComponent,
    PaginationStatusBarComponent,
    AkaunPinNumberDialogComponent
  ],
  imports: [
    CommonModule,
    MaterialModule,
    FormsModule,
    ReactiveFormsModule,
    FlexLayoutModule,
    PerfectScrollbarModule,
    NgxMatSelectSearchModule,
    AgGridModule,
    StoreModule.forFeature(columnViewModelFeatureKey, columnViewModelReducers),
    StoreModule.forFeature(columnViewModelFeatureKey1, columnViewModelReducers1),
    StoreModule.forFeature(columnViewModelFeatureKey2, columnViewModelReducers2),
    StoreModule.forFeature(columnViewModelFeatureKey3, columnViewModelReducers3),
    StoreModule.forFeature(columnViewModelFeatureKey4, columnViewModelReducers4),
  ],
  exports: [
    AdvancedSearchComponent,
    PaginationComponent,
    AppletLogoComponent,
    ColumnToggleComponent,
    GridToggleComponent,
    BreadcrumbComponent,
    CurrencyComponent,
    ForexDataSourceComponent,
    CreditTermsComponent,
    SelectSettlementMethodComponent,
    SalesAgentComponent,
    SalesAgentSearchComponent,
    SalesManComponent,
    SSTComponent,
    WHTComponent,
    MaterialModule,
    FormsModule,
    ReactiveFormsModule,
    FlexLayoutModule,
    PerfectScrollbarModule,
    NgxMatSelectSearchModule,
    DragDropDirective,
    FirstColumnDirective,
    SecondColumnDirective,
    PermissionDirective,
    PricingSchemeUOMComponent,
    SelectSetOfBooksDropDownComponent,
    AdvancedSearchLinesComponent,
    LineContainerKoByComponent,
    LineContainerKoForComponent,
    LineContainerKoForQueueComponent,
    DocLinkKoForComponent,
    DocLinkKoByComponent,
    UOMComponent,
    SelectMultiLocationDropDownComponent,
    SelectMultiLocationDropDownStockReportComponent,
    PaginationClientSideComponent,
    SelectGlcodeDropDownComponent,
    GenericDocumentPostingComponent,
    JournalTxnComponent,
    CashbookTxnComponent,
    TaxTxnComponent,
    PointsTxnComponent,
    InvTxnComponent,
    PermissionHideDirective,
    SelectSubledgerComponent,
    AdvancedSearchV2Component,
    BranchComponent,
    // SelectVehiclesDropdownComponent,
    LocationFromComponent,
    LocationToComponent,
    AppletComponent,
    SelectMultiBranchDropDownComponent,
    WorkflowProcessComponent,
    WorkflowActionComponent,
    WorkflowActionTypeComponent,
    PaginationClientSideV2Component,
    SelectPrintableFormatAppletLoginEpComponent,
    SalesManAppletLoginComponent,
    SelectTariffCodeComponent,
    UtilItemSerialNumberComponent,
    SerialNumberScanComponent,
    SerialNumberListingComponent,
    SerialNumberImportComponent,
    PricingSchemeComponent,
    PersonInChargeComponent,
    SelectPrintableFormatComponent,
    UtilLocationDropdownComponent,
    PaginationV2Component,
    CommissionCycleDropDownComponent,
    LocationDropdownStockComponent,
    SelectMultiCompanyDropDownComponent,
    SelectMultiEntityDropDownComponent,
    PaginationServerSideV2Component,
    AdvancedSearchEntitiesComponent,
    AdvancedSearchGeneralComponent,
    SelectMultiLabelListDropDownComponent,
    SelectMultiItemCategoryLevelDropDownComponent,
    RecurrenceEditorComponentComponent,
    EInvoiceRecurrenceEditorComponentComponent,
    ExportStatusBarComponent,
    ExportButtonComponent,
    ParentPrintableDropdownComponent,
    SelectEmailTemplateComponent,
    AdvancedSearchCashbookComponent,
    SelectGLCodeV2Component,
    MonthYearPickerComponent,
    WeekPickerComponent,
    SelectMultiCustomerDropDownComponent,
    SelectMultiCustomerCategoryDropDownComponent,
    WarehouseAdvancedSearchComponent,
    SelectEmployeeDropDownComponent,
    SelectWarehouseDropDownComponent,
    CompanyWorkflowDropdownComponent,
    PricingSchemeV2Component,
    RefreshComponent,
    UOMV2Component,
    RequireDeliveryCellRendererComponent,
    DeliveryTypeCellRendererComponent,
    SelectMultiCurrencyDropDownComponent,
    UomWmsItemComponent,
    CustomTooltipComponent,
    CustomLoadingOverlayComponent,
    PaginationClientSideV3Component,
    AdvancedSearchWmsItemsComponent,
    SelectIntercompanyBranchDownComponent,
    PurchaserComponent,
    CashbookAdvancedSearchDropdownComponent,
    SelectGLCategryDropDownComponent,
    SerialNumberOptionalComponent,
    SerialNumberImportOptionalComponent,
    SerialNumberScanOptionalComponent,
    SelectMultiGLCodeDropDownComponent,
    SelectMultiDeviceDropDownComponent,
    SelectMultiChartOfAccountDropDownComponent,
    SelectMultiGLCategoryDropDownComponent,
    ButtonRendererComponent,
    AkaunConfirmationDialogComponent,
    AkaunMessageDialogComponent,
    WmsContainerAdvancedSearchDropdownComponent,
    WmsLayoutNodeAdvancedSearchDropdownComponent,
    TimezoneDropdownComponent,
    PrintablePageComponent,
    SelectPricebookComponent,
    SelectMultiGLSectionDropDownComponent,
    SelectLocationDropDownLatestComponent,
    SelectNSTIDropDownComponent,
    SelectPrintableFormatWithFilterComponent,
    SelectBudgetItemComponent,
    SelectBudgetRegisterComponent,
    SelectBudgetFiscalYearComponent,
    SelectBudgetVotebookGenDocComponent,
    SelectBudgetRegisterGenDocComponent,
    SelectBudgetFiscalPeriodGenDocComponent,
    SelectBudgetItemGenDocComponent,
    SelectBudgetFiscalYearGenDocComponent,
    AdvancedSearchClientSideComponent,
    DefaultLanguageSelectDropDownComponent,
    AkaunDiscardDialogComponent,
    EinvoiceUOMComponent,
    SelectMultiEmployeeDropDownComponent,
    AkaunVoidDialogComponent,
    SelectMultiDropDownComponent,
    SelectMultiFiItemDropDownComponent,
    CloseConfirmDialogComponent,
    RejectionReasonPopUpComponent,
    SelectMultiPricingSchemeDropDownComponent,
    SelectMultiPricingSchemeDropDownComponent,
    SelectMultiLocationLabelDropDownComponent,
    SelectMultiPricingSchemeDropDownComponent,
    SelectCustomerDropdownComponent,
    RoleDropdownComponent,
    AdvancedSearchLinesV2Component,
    SelectMultiItemCategoryDropDownComponent,
    ConsulationNoDropdownComponent,
    ConsulationNameDropdownComponent,
    VehicleNoDropdownComponent,
    VehicleBrandNameDropdownComponent,
    VehicleCapacityDropdownComponent,
    VehicleModelYearDropdownComponent,
    SelectMultiCashbookDropDownComponent,
    EscapeHtmlPipe,
    SelectItemGlCodeComponent,
    SelectMultiTaxCodeDropDownComponent,
    AdvancedSearchEInvoiceComponent,
    AdvancedSearchEInvoicePortalComponent,
    BranchDropdownSimpleComponent,
    CustomerDropdownSimpleComponent,
    ExportDataDialogComponent,
    SelectSupplierDropdownComponent,
    SelectMultiLocationDropdownComponent,
    PaginationV2EntityComponent,
    AkaunGenDocLockDialogComponent,
    MultiLevelDiscountComponent,
    GenericDocumentViewMainComponent,
    GenericDocumentViewLineComponent,
    GenericDocumentViewSettlementComponent,
    GenericDocumentViewExportComponent,
    CashDocumentViewMainComponent,
    CashDocumentViewLineComponent,
    CashDocumentPostingComponent,
    CashDocumentPostingJournalTxnComponent,
    CashDocumentPostingCashbookTxnComponent,
    CashDocumentViewExportComponent,
    SupplierDropdownSimpleComponent,
    CellClickHandlerComponent,
    GenericDocumentViewComponent,
    SelectRoundingItemComponent,
    SelectGroupDiscountItemComponent,
    SelectDefaultSettlementItemComponent,
    UtilPricingSchemeHdrComponent,
    SelectPosCardTerminalDropDownComponent,
    CurrencyRateComponent,
    SelectMultiEmployeeDropDownV2Component,
    AdvancedSearchPermSetComponent,
    SelectMultiCompanyDropDownEinvoiceComponent,
    SelectBudgetVotebookComponent,
    VotebookDropdownSimpleComponent,
    FiscalYearDropdownSimpleComponent,
    BudgetLineDropdownSimpleComponent,
    AkaunGenericDocViewDialogComponent,
    SelectMultiEntityDropDownComponentV2,
    SelectMultiVehicleDropDownComponent,
    DocLinkViewComponent,
    DocLinkViewCopyToComponent,
    DocLinkViewCopyFromComponent,
    ContraViewListingComponent,
    AttachmentViewComponent,
    ArapViewComponent,
    AdvancedSearchOpqComponent,
    SelectMultiTaxFilingDropDownComponent,
    MultiMemberSelectionDropDownComponent,
    CommonEntityDropDownComponent,
    PermittedUsersByClientSidePermDropdownComponent,
    AgGridCustomComponent,
    StateDropDownStatusBarComponent,
    SelectMultiInvItemDropDownComponent,
    PaginationStatusBarComponent,
    AkaunPinNumberDialogComponent
  ],
  providers: [
    { provide: MAT_SNACK_BAR_DEFAULT_OPTIONS, useValue: { duration: 2500 } },
    { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS },
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS]
    },
  ],
})
export class UtilitiesModule {
  static currencyFormatter(currency, returnValue?, decimalPlaces = 2, blanket = true) {
    if (currency === null || currency === undefined) return returnValue;

    const parsedCurrency = parseFloat(currency);
    if (isNaN(Number(parsedCurrency))) return currency;

    const roundedCurrency = parseFloat(parsedCurrency.toFixed(decimalPlaces));

    // Treat very small negative values rounded to 0 as 0
    if (roundedCurrency === 0) {
      return roundedCurrency.toFixed(decimalPlaces);
    }

    const formatted = roundedCurrency.toLocaleString('en-US', {
      minimumFractionDigits: decimalPlaces,
      maximumFractionDigits: decimalPlaces
    });

    const value =
      formatted.charAt(0) === "-" && blanket
        ? "(" + formatted.substring(1, formatted.length) + ")"
        : formatted;

    return `${value}`;
  }

  static dateFormatter(date, format = "YYYY-MM-DD") {
    let formatted = moment(date).format(format);
    if (formatted === "Invalid date") {
      formatted = null;
    }
    return `${formatted}`;
  }

  static filterDateParams = {
    comparator: function (filterLocalDateAtMidnight: Date, cellValue: string) {
      var dateAsString = cellValue;
      if (dateAsString == null) return -1;
      if (dateAsString.indexOf("T")) {
        dateAsString = dateAsString.substring(0, dateAsString.indexOf("T"));
      }
      var dateParts =
        dateAsString.indexOf("/") > 0
          ? dateAsString.split("/")
          : dateAsString.split("-");
      var cellDate = new Date(
        Number(dateParts[0]),
        Number(dateParts[1]) - 1,
        Number(dateParts[2])
      );
      if (filterLocalDateAtMidnight.getTime() === cellDate.getTime()) {
        return 0;
      }
      if (cellDate < filterLocalDateAtMidnight) {
        return -1;
      }
      if (cellDate > filterLocalDateAtMidnight) {
        return 1;
      }
    },
    browserDatePicker: true,
    buttons: ["reset"],
    minValidYear: 2000,
    maxValidYear: 2099,
  };

  static filterTextParams = {
    filters: [
      {
        filter: 'agTextColumnFilter',
        display: 'subMenu'
      },
      {
        filter: 'agSetColumnFilter',
        filterParams: {
          buttons: ['reset'],
        }
      }
    ]
  };

  static columnTypes = {
    integerColumn: {
      type: 'numericColumn',
      aggFunc: 'sum',
      enableValue: true,
      filter: 'agNumberColumnFilter',
      cellStyle: { textAlign: 'right' },
      headerClass: 'ag-right-aligned-header',
      valueFormatter: params => params.value ? params.value : '0',
      pdfExportOptions: {
        headerStyles: {
          alignment: 'right'
        },
        styles: {
          alignment: 'right',
        }
      }
    },
    integerCenterColumn: {
      type: 'numericColumn',
      aggFunc: 'sum',
      enableValue: true,
      filter: 'agNumberColumnFilter',
      cellStyle: { textAlign: 'center' },
      headerClass: 'ag-center-aligned-header',
      valueFormatter: params => params.value ? params.value : '0',
      pdfExportOptions: {
        headerStyles: {
          alignment: 'center'
        },
        styles: {
          alignment: 'center',
        }
      }
    },
    decimalColumn: {
      type: 'numericColumn',
      aggFunc: 'sum',
      enableValue: true,
      filter: 'agNumberColumnFilter',
      cellStyle: { textAlign: 'right' },
      headerClass: 'ag-right-aligned-header',
      valueFormatter: params => params.value ? UtilitiesModule.currencyFormatter(params.value) : '0.00',
      pdfExportOptions: {
        headerStyles: {
          alignment: 'right'
        },
        styles: {
          alignment: 'right',
          currencyFormatter: 'true'
        }
      }
    },
    decimalColumnBlank: {
      type: 'numericColumn',
      aggFunc: 'sum',
      enableValue: true,
      filter: 'agNumberColumnFilter',
      cellStyle: { textAlign: 'right' },
      headerClass: 'ag-right-aligned-header',
      valueFormatter: params => params.value ? UtilitiesModule.currencyFormatter(params.value) : '',
      pdfExportOptions: {
        headerStyles: {
          alignment: 'right'
        },
        styles: {
          alignment: 'right',
          currencyFormatter: 'true'
        }
      }
    },
    decimalColumnNoBlanket : {
      type: 'numericColumn',
      aggFunc: 'sum',
      enableValue: true,
      filter: 'agNumberColumnFilter',
      cellStyle: { textAlign: 'right' },
      headerClass: 'ag-right-aligned-header',
      valueFormatter: params => params.value ? UtilitiesModule.currencyFormatter(params.value, 0.00, 2, false) : '0.00',
      pdfExportOptions: {
        headerStyles: {
          alignment: 'right'
        },
        styles: {
          alignment: 'right',
          currencyFormatter: 'true'
        }
      }
    },
    decimal8Column: {
      type: 'numericColumn',
      aggFunc: 'sum',
      enableValue: true,
      filter: 'agNumberColumnFilter',
      cellStyle: { textAlign: 'right' },
      headerClass: 'ag-right-aligned-header',
      valueFormatter: params => params.value ? UtilitiesModule.currencyFormatter(params.value, '0.00000000', 8) : '0.00000000',
      pdfExportOptions: {
        headerStyles: {
          alignment: 'right'
        },
        styles: {
          alignment: 'right',
          currencyFormatter: 'true'
        }
      }
    },
    dateColumn: {
      cellStyle: { textAlign: 'left' },
      valueFormatter: params => params.value ? UtilitiesModule.dateFormatter(params.value, 'YYYY-MM-DD') : '',
      filter: 'agDateColumnFilter',
      filterParams: UtilitiesModule.filterDateParams,
      pdfExportOptions: {
        headerStyles: {
          alignment: 'left'
        },
        styles: {
          alignment: 'left',
          dateFormatter: 'true'
        }
      }
    },
    dateTimeColumn: {
      cellStyle: { textAlign: 'left' },
      valueFormatter: params => params.value ? UtilitiesModule.dateFormatter(params.value, 'YYYY-MM-DD HH:mm:ss') : '',
      filter: 'agDateColumnFilter',
      filterParams: UtilitiesModule.filterDateParams,
      pdfExportOptions: {
        headerStyles: {
          alignment: 'left'
        },
        styles: {
          alignment: 'left',
          dateTimeFormatter: 'true'
        }
      }
    },
    timeColumn: {
      cellStyle: { textAlign: 'left' },
      valueFormatter: params => params.value ? UtilitiesModule.dateFormatter(params.value, 'HH:mm:ss') : '',
      filter: 'agMultiColumnFilter',
      filterParams: UtilitiesModule.filterTextParams
    },
    textColumn: {
      cellStyle: { textAlign: 'left' },
      filter: 'agMultiColumnFilter',
      filterParams: UtilitiesModule.filterTextParams
    }
  }

  static getTodayNoTime() {
    // const date = new Date();
    // date.setHours(0, 0, 0, 0);
    // return date;
    return moment().startOf('day');
  }

  static getDateNoTime(val: any) {
    // const date = new Date(val);
    // date.setHours(0, 0, 0, 0);
    // return date;
    const date = moment(val).startOf('day');
    return date;
  }

  static getTodayEndTime() {
    // const date = new Date();
    // date.setHours(0, 0, 0, 0);
    // return date;
    return moment().endOf('day');
  }

  static getDateEndTime(val: any) {
    // const date = new Date(val);
    // date.setHours(23, 59, 59, 999);
    // return date;

    const date = moment(val).endOf('day');
    return date;
  }

  static getFirstDay() {
    // const now = new Date();
    // const date = new Date(now.getFullYear(), now.getMonth(), 1);
    // date.setHours(0, 0, 0, 0);
    // return date;
    const date = moment().startOf('month').startOf('day');
    return date;
  }

  static getLastDay() {
    // const now = new Date();
    // const date = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    // date.setHours(23, 59, 59, 999);
    // return date;
    const date = moment().endOf('month').endOf('day');
    return date;
  }

  static getLastDayMonth(year, month) {
    const date = moment(`${year}-${month}`, 'YYYY-M').endOf('month');
    return date;
  }

  static checkNull(obj, returnValue) {
    return obj ? obj : returnValue;
  }

  static checkNullDate(obj: any, returnValue: string): string {
    if (!obj) {
      return returnValue;
    }

    // Ensure obj is a Date object
    const dateObj = obj instanceof Date ? obj : new Date(obj);

    if (isNaN(dateObj.getTime())) {
      return returnValue; // If conversion fails, return default value
    }

    // Extract year, month, and day from the Malaysia Time (UTC+8)
    const year = dateObj.getFullYear();
    const month = dateObj.getMonth(); // Month is zero-based (0 = January)
    const day = dateObj.getDate();

    // Create a new Date object explicitly in Malaysia Time (UTC+8)
    const malaysiaDate = new Date(Date.UTC(year, month, day, 0, 0, 0)); // Midnight Malaysia Time

    return malaysiaDate.toISOString(); // Format: YYYY-MM-DDTHH:mm:ss.SSSZ
  }

  static checkSerialValid(serialArray: any[]) {
    let hasInvalidSerial = false;
    if (serialArray) {
      if (Array.isArray(serialArray)) {
        if (serialArray.filter(s => s.status === "INVALID").length) {
          hasInvalidSerial = true;
        }
        if (!serialArray.length) {
          hasInvalidSerial = true;
        }
      } else {
        let oldArray: any = serialArray;
        if (!oldArray?.serialNumbers)
          hasInvalidSerial = true;
      }
    } else {
      hasInvalidSerial = true;
    }
    return hasInvalidSerial;
  }

  static checkSerialDuplicate(docline: bl_fi_generic_doc_line_RowInterface[]) {
    let hasDuplicateSerial = false;
    docline.forEach(line => {
      if (line.item_sub_type === "SERIAL_NUMBER") {
        if (line.serial_no) {
          if (Array.isArray(line.serial_no)) {
            if (line.serial_no.filter(s => s.remarks === "Duplicate Serial Number").length) {
              hasDuplicateSerial = true;
            }
          }
        }
      }
    })


    return hasDuplicateSerial;
  }

  static getDropDownGuids(value: any, isMultiple = true) {
    if (isMultiple) {
      const values = UtilitiesModule.checkNull(value, '[]');
      if (Array.isArray(values)) {
        return [...values.map(item => item.guid)];
      }
      else {
        return [];
      }
    }
    else {
      return value.guid;
    }
  }

  static removeDuplicate<T>(array: T[], key: string): T[] {
    const map = new Map();
    for (const item of array) {
      map.set(item[key], item);
    }
    return Array.from(map.values());
  }

  static generateEANCode() {
    const date = this.dateFormatter(new Date(), 'YYYYMMDDHHmmss');
    const key = this.calculateEANKey(date);
    return date + key;
  }

  static calculateEANKey(eanCode: string) {
    let key = 0
    for (let i = 0; i < eanCode.length; i++) {
      let char = eanCode[i]
      let number = Number.parseInt(char)
      if (i % 2 == 0) {
        key += number
      } else {
        key += 3 * number
      }
    }
    key %= 10
    key = (10 - key) % 10
    return key.toString()
  }

  static divide(num1, num2) {
    if (num1 && num2) {
      if (num2 === 0) return 0;
      return num1 / num2;
    }
    return 0;
  }

  static getSystemDefault(guid, name) {
    if (guid === '00000000-0000-0000-0000-000000000000') {
      return 'SYSTEM DEFAULT';
    }
    else {
      return name;
    }
  }

  static exportData(data: any[], filename: string, columnsDefs: any[]) {
    if (!data) return;

    const formatCellValue = (column, item) => {
      if (column.type === 'dateColumn') {
        return UtilitiesModule.dateFormatter(item[column.field], 'YYYY-MM-DD');
      } else if (column.type === 'dateTimeColumn') {
        return UtilitiesModule.dateFormatter(item[column.field], 'YYYY-MM-DD HH:mm:ss');
      } else {
        return item[column.field];
      }
    };

    const exportData = data.map(item => ({
      ...columnsDefs.reduce((acc, column) => {
        acc[column.headerName] = formatCellValue(column, item);
        return acc;
      }, {}),
    }));

    UtilitiesModule.exportCSV(exportData, filename);
  }

  static exportCSV(data: any[], fileName: string) {
    const csv = UtilitiesModule.convertToCSV(data);

    // Create a Blob with UTF-8 encoding
    const blob = new Blob([new Uint8Array([0xEF, 0xBB, 0xBF]), csv], { type: 'text/csv;charset=utf-8' });

    // Create a URL for the Blob
    const url = window.URL.createObjectURL(blob);

    // Create a link element to trigger the download
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;

    // Trigger the download
    document.body.appendChild(a);
    a.click();

    // Clean up
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  }

  static convertToCSV(data: any[]): string {
    const header = Object.keys(data[0]).join(',');

    const rows = data.map(item => {
      return Object.values(item).map(value => {
        if (typeof value === 'string') {

          // Use a type assertion with a non-null assertion operator
          let value2 = value as string;

          // Escape double quotes by doubling them
          value2 = value2.replace(/"/g, '""');

          // Wrap the value in double quotes if it contains a comma or double quotes
          if (value2.includes(',') || value2.includes('"') || /[^\x00-\x7F]/.test(value2)) {
            value = `"${value2}"`;
          }
          else {
            value = value2;
          }
        }
        return value;
      }).join(',');
    });

    return [header, ...rows].join('\n');
  }

  static isValidGuid(value: string): boolean {
    const guidRegex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
    return !!value && typeof value === 'string' && guidRegex.test(value);
  }

  static getDefaultExportParams() {
    return {
      processCellCallback: params => {
        if (params.column.getColDef().type === 'dateColumn') {
           return params.value ? UtilitiesModule.dateFormatter(params.value) : '';
        }
        else if (params.column.getColDef().type === 'dateTimeColumn') {
          return params.value ? UtilitiesModule.dateFormatter(params.value, 'YYYY-MM-DD HH:mm:ss') : '';
        }
        return params.value;
      }
    };
  }

  static isNullOrEmptyObject(value: any): boolean {
    return value === null || (typeof value === 'object' && Object.keys(value).length === 0);
  }

  static concatenateWithDelimiter(arr: string[], delimiter: string): string {
    return arr.filter(str => (str && str.trim() !== '')).join(delimiter);
  }

  static getJournalLineDescr(arr: string[]) {
    const filter = ['AUTO CREATED FROM JOURNAL POSTING JOB PROCESSOR','AUTO CREATED FROM MONTH END PROCESSING',
      'AUTO CREATED FROM RETAINED EARNINGS PROCESSING', 'AUTO CREATED FROM COGS PROCESSING', 'AUTO CREATED FROM REVERSE JOURNAL POSTING JOB PROCESSOR']

    return arr.filter(str => (str && str.trim() !== '' && filter.indexOf(str) < 0)).join('|');
  }

  static convertToTimestampTz(date: string | Date): TimestampTzDatatypeInterface {
    const newDate = new Date(date) as TimestampTzDatatypeInterface;
    return newDate;
  }

  static autoSizeAllColumns(params) {
    const allColumnIds: string[] = params.columnApi.getAllColumns().map(column => column.getColId());

     // Auto-size all columns
     params.columnApi.autoSizeColumns(allColumnIds);

    const allColumns = params.columnApi.getAllColumns();
    allColumns.forEach(column => {
      const currentWidth = column.getActualWidth(); // Get the actual width of the column
      // Set width to 200 if the current width exceeds 200
      if (currentWidth > 200) {
        params.columnApi.setColumnWidth(column.getColId(), 200);
      }
    });
  }

  static onDecimalKeyPress(event: KeyboardEvent, decimal: number = 2) {
    const input = event.target as HTMLInputElement;
    const inputChar = String.fromCharCode(event.charCode);

    // Allow only numbers and decimal points
    if (!/^[0-9.]$/.test(inputChar)) {
      event.preventDefault(); // Disallow non-numeric characters
    }

    // Ensure only one decimal point is allowed
    if (input.value.includes('.') && inputChar === '.') {
      event.preventDefault(); // Disallow multiple decimal points
    }

    // Prevent input if decimal part exceeds the specified limit
    const parts = input.value.split('.');
    if (parts[1]?.length >= decimal && input.selectionStart > input.value.indexOf('.')) {
      event.preventDefault(); // Block input beyond 2 decimal places
    }
  }

  static getTargetsByPermission(userPermissionTarget$, readPermission, targetTable) {
    let targets = [];

    userPermissionTarget$.subscribe(permissionObject => {
      const hasAdminPermission = permissionObject.some(p =>
        (p.permDfn === 'TNT_TENANT_ADMIN' || p.permDfn === 'TNT_TENANT_OWNER') && p.hasPermission
      );

      if (hasAdminPermission) {
        targets = [];
        return;
      }

      const readTarget = permissionObject.find(p => p.permDfn === readPermission);
      targets = readTarget?.target?.[targetTable] ?? [];
    });
    return targets;
  }

  static hasTargetPermission(userPermissionTarget$: Observable<any[]>, permDfn: string): Promise<boolean> {
    return new Promise((resolve) => {
      userPermissionTarget$.pipe(take(1)).subscribe((permissions) => {

        const matched = permissions.find(p => p.permDfn === permDfn);
        const isAdmin = permissions.find(p => p.permDfn === 'TNT_TENANT_ADMIN')?.hasPermission;
        const isOwner = permissions.find(p => p.permDfn === 'TNT_TENANT_OWNER')?.hasPermission;

        resolve(!!isAdmin || !!isOwner || !!matched?.hasPermission);
      });
    });
  }

  static getServiceName(serverDocType: string): string {
    const docType = serverDocType.toLowerCase();

    if (docType.includes("sales") || docType.includes("delivery") ||
        docType.includes("purchase_return") || docType.includes("purchase_credit_note") ||
        docType.includes("purchase_debit_note")) {
      return "CP_COMMERCE_INTERNAL_SALES_ORDERS_JASPER_PRINT_SERVICE";
    }

    if (docType.includes("blanket_purchase_order")) {
      return "BLANKET_PURCHASE_ORDER_JASPER_PRINT_SERVICE";
    }

    if (docType.includes("purchase_debit_note") || docType.includes("purchase_credit_note")
    || docType.includes("purchase_return")) {
      return "INTERNAL_PURCHASE_ORDER_PRINT_SERVICE";
    }

    if (docType.includes("purchase")) {
      return "INTERNAL_PURCHASE_ORDER_PRINT_SERVICE";
    }

    if (docType.includes("payment_voucher") || docType.includes("receipt_voucher")) {
      return "PAYMENT_RECEIPT_VOUCHER_PRINT_SERVICE";
    }

    return "GENERICE_DOCUMENT_JASPER_PRINT_SERVICE"; // Default service
  }

  static matchTodayOrReturn(dateTxn: any): any {
    const txnDate = moment(dateTxn).startOf('day');
    const today = moment().startOf('day');

    return txnDate.isSame(today) ? moment() : dateTxn;
  }

  static getDayCount(date1: string | Date, date2: string | Date): number | null {
    const d1 = moment(date1);
    const d2 = moment(date2);

    if (!d1.isValid() || !d2.isValid()) {
      return null;
    }

    const diffDays = Math.abs(d2.diff(d1, 'days')) + 1;
    return diffDays;
  }

  static isMobileView(): boolean {
    return window.innerWidth <= 768; // You can adjust the breakpoint
  }
}
