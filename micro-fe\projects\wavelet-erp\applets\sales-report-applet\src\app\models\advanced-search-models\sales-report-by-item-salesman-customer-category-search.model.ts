import { FormControl, FormGroup, Validators } from '@angular/forms';
import { SearchModelV2 } from 'projects/shared-utilities/models/search-model-v2';
import { UtilitiesModule } from 'projects/shared-utilities/utilities/utilities.module';

export const SalesReportByItemSalesmanCustomerCategorySearchModel: SearchModelV2 = {
  label: {
    branch: 'Branch',
    salesman: 'Salesman',
    date: 'Date',
    calculateBaseOn: 'Calculate Base On',
    groupBy1: 'Group By 1',
    groupBy2: 'Group By 2'
  },
  dataType: {
    branch: 'select-multi-branch',
    salesman: 'select-multi-entity',
    date: 'date',
    calculateBaseOn: ['select', [
      'cost_ma', 'cost_wa', 'cost_fifo', 'cost_lifo', 'cost_replacement', 'cost_manual',
      'ref_price1','ref_price2','ref_price3',
      'delta_price1','delta_price2','delta_price3',
      'rebate_price1','rebate_price2','rebate_price3',
      'sales_min_price','sales_max_price',
      'purchase_min_price','purchase_max_price'
    ]],
    groupBy1: ['select', ['Level 1','Level 2','Level 3','Level 4','Level 5','Level 6','Level 7','Level 8','Level 9','Level 10']],
    groupBy2: ['select', ['Level 1','Level 2','Level 3','Level 4','Level 5','Level 6','Level 7','Level 8','Level 9','Level 10']]
  },
  options: {
    branch: {'multiple': true},
    salesman: {'multiple': true, 'entity': 'employee'},
    date: {'required': true},
    calculateBaseOn: {'required': true},
    groupBy1: {'required': true},
    groupBy2: {'required': true},
  },

  form: new FormGroup({
    branch: new FormControl(),
    salesman: new FormControl(),
    calculateBaseOn: new FormControl('cost_ma'),
    date: new FormGroup({
      from: new FormControl(UtilitiesModule.getTodayNoTime()),
      to: new FormControl(UtilitiesModule.getTodayNoTime())
    }),
    groupBy1: new FormControl(),
    groupBy2: new FormControl()
  })
};