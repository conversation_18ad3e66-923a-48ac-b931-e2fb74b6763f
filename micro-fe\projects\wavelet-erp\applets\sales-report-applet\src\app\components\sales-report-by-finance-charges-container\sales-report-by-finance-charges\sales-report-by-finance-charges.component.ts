// Angular Core
import { ChangeDetectionStrategy, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';

// NgRx
import { Store } from "@ngrx/store";
import { ComponentStore } from '@ngrx/component-store';

// Third-party Libraries
import { GridOptions } from 'ag-grid-enterprise';
import { ErrorMessages } from 'blg-akaun-ts-lib';
import { ToastrService } from 'ngx-toastr';
import { Observable, from } from 'rxjs';
import { map, mergeMap, toArray, take } from 'rxjs/operators';
import { SubSink } from 'subsink2';

// Session Controller
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionActions } from 'projects/shared-utilities/modules/session/session-controller/actions';

// Permissions
import { PermissionStates } from 'projects/shared-utilities/modules/permission/permission-controller';
import { UserPermInquirySelectors } from 'projects/shared-utilities/modules/permission/user-permissions-inquiry-controller/selectors';

// Shared Utilities
import { SearchQueryModel } from 'projects/shared-utilities/models/query.model';
import { UtilitiesModule } from 'projects/shared-utilities/utilities/utilities.module';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { AppConfig } from 'projects/shared-utilities/visa';

// Application Imports
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { SalesReportByItemCodeSearchModel } from '../../../models/advanced-search-models/sales-report-by-item-code-search.model';
import { SalesReportByItemCodeInputModel, SalesReportByItemCodeModel } from '../../../models/sales-report-by-item-code-model';
import { ApiService } from '../../../services/api-service';

interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
}

@Component({
  selector: 'app-sales-report-by-finance-charges',
  templateUrl: './sales-report-by-finance-charges.component.html',
  styleUrls: ['./sales-report-by-finance-charges.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})

export class SalesReportByFCComponent extends ViewColumnComponent implements OnInit, OnDestroy {

  protected subs = new SubSink();

  compId= 'salesReportByFinanceCharges';
  compName = 'Sales Report By Finance Charges';
  protected readonly index = 0;
  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  protected localState: LocalState;

  readonly deactivateAdd$ = this.componentStore.select(state => state.deactivateAdd);
  readonly deactivateList$ = this.componentStore.select(state => state.deactivateList);

  readonly userPermissionTarget$ = this.permissionStore.select(
    UserPermInquirySelectors.selectUserPermInquiry
  );

  toggleColumn$: Observable<boolean>;
  searchModel = SalesReportByItemCodeSearchModel;

  showColumns = [
    { name: 'sales_cost', setting: 'HIDE_MA_COST', permission: 'SHOW_MA_COST' },
    { name: 'gp', setting: 'HIDE_GP', permission: 'SHOW_GP' },
    { name: 'gp_percentage', setting: 'HIDE_GP_PERCENTAGE', permission: 'SHOW_GP_PERCENTAGE' },
    { name: 'landed_ma_amount', setting: 'HIDE_MA_COST', permission: 'SHOW_MA_COST' },
    { name: 'gp_landed', setting: 'HIDE_GP', permission: 'SHOW_GP' },
    { name: 'gp_percentage_landed', setting: 'HIDE_GP_PERCENTAGE', permission: 'SHOW_GP_PERCENTAGE' },
  ]

  rowData = [];
  totalRecords = 0;
  salesCost = 'cost_ma';
  branchGuids = [];

  // api visa
  apiVisa = AppConfig.apiVisa;

  // initial grid state
  gridApi;
  gridColumnApi;
  
  gridOptions: GridOptions = {
    pagination: false,
    groupIncludeTotalFooter: true,
  };

  columnsDefs = [
    {
      headerName: 'Branch',
      field: 'branch_code',
      type: 'textColumn'
    },
    {
      headerName: 'Location',
      field: 'location_code',
      rowGroup: false, hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Item Code',
      field: 'item_code',
      type: 'textColumn'
    },
    {
      headerName: 'Item Name',
      field: 'item_name',
      type: 'textColumn'
    },
    {
      headerName: 'Type',
      field: 'type',
      type: 'textColumn'
    },
    {
      headerName: 'UOM',
      field: 'uom',
      type: 'textColumn'
    },

    {
      headerName: 'Qty Sold',
      field: 'qty_sold',
      type: 'integerColumn'
    },
    /* {
      headerName: 'Qty Return',
      field: 'qty_return',
      type: 'numericColumn',
      aggFunc: 'sum',
      enableValue: true,
      filter: 'agNumberColumnFilter',
    }, */
    {
      headerName: 'Unit Price Std',
      field: 'unit_price_std',
      type: 'decimalColumn'
    },
    {
      headerName: 'Unit Disc',
      field: 'unit_discount',
      type: 'decimalColumn'
    },
    {
      headerName: 'Unit Price Net',
      field: 'unit_price_net',
      type: 'decimalColumn'
    },
    {
      headerName: 'Amount Net',
      field: 'amount_net',
      type: 'decimalColumn'
    },
    {
      headerName: 'Fin Rate',
      field: 'fin_rate',
      type: 'decimalColumn'
    },
    {
      headerName: 'Fin Charge Amt',
      field: 'finance_charge_amount',
      type: 'decimalColumn'
    },
    {
      headerName: 'Sales Cost',
      field: 'sales_cost',
      type: 'decimalColumn'
    },
    {
      headerName: 'GP',
      field: 'gp',
      type: 'decimalColumn'
    },
    {
      headerName: 'GP%',
      field: 'gp_percentage',
      type: 'decimalColumn',
      aggFunc: '',
      valueGetter: (params) => {
        const cost = this.calculateTotal(params.node, 'gp');
        const salesAmount = this.calculateTotal(params.node, 'amount_net');

        if (cost && salesAmount && salesAmount !== 0) {
          return (cost / salesAmount) * 100;
        }
        return 0;
      },

    }
  ];

  readPermissionDefintion = {
    branch: 'API_TNT_DM_ERP_SALES_REPORT_BY_FINANCE_CHARGES_READ'
  }

  constructor(
    protected viewColFacade: ViewColumnFacade,
    protected apiService: ApiService,
    protected toastr: ToastrService,
    protected readonly componentStore: ComponentStore<LocalState>,
    private readonly permissionStore: Store<PermissionStates>,
    protected readonly sessionStore: Store<SessionStates>) {
    super();
  }

  ngOnInit() {
    // Initialize branch GUIDs based on permissions
    this.branchGuids = UtilitiesModule.getTargetsByPermission(this.userPermissionTarget$,
      this.readPermissionDefintion.branch, 'bl_fi_mst_branch'
    );

    this.toggleColumn$ = this.viewColFacade.toggleColumn$;
    this.subs.sink = this.viewColFacade.rowDataByFC$.subscribe(rowData => this.rowData = rowData);
    this.subs.sink = this.viewColFacade.totalRecordsByFC$.subscribe(totalRecords => this.totalRecords = totalRecords);
    this.subs.sink = this.localState$.subscribe(a => {
      this.localState = a;
      this.componentStore.setState(a);
    });
  }
  
  onNext() {
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState, deactivateAdd: true, deactivateList: false
    });
    this.viewColFacade.onNextAndReset(this.index, 1);
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.closeToolPanel();

    if (this.rowData.length > 0) {
      this.setDataRowCache();
    }
    else
    {
      //this.createData();
    }

    const statusBarComponent = this.gridApi.getStatusPanel('statusBarExportKey');
    statusBarComponent.setTitle(this.compName);
    statusBarComponent.setFilename(this.compName);
    statusBarComponent.setHideGroupColumn(true);
  }

  createData(inputModel?: SalesReportByItemCodeInputModel) {
    console.log("on create data....");
    this.clear();
    this.viewColFacade.loadInit(this.gridApi);
    this.subs.sink = this.apiService.getSalesReportByFC(inputModel, this.apiVisa).pipe(
      mergeMap(a => from(a.data).pipe(
        map(b => {
          const sales_cost = this.getBaseAmt(b, this.getSalesCost(b));
          const amount_std = this.getBaseAmt(b, b.amount_std);
          const unit_price_std = this.getBaseAmt(b, b.unit_price_std);
          const amount_net = this.getBaseAmt(b, b.amount_net);
          const unit_discount = this.getBaseAmt(b, b.unit_discount);
          const unit_price_net = this.getBaseAmt(b, b.unit_price_net);
          Object.assign(b,
            {
              sales_cost: sales_cost,
              gp: amount_net - sales_cost,
              gp_percentage: Number(amount_net) !== 0 ? (amount_net - sales_cost) * 100 / amount_net : 0,
              unit_cost: UtilitiesModule.divide(sales_cost, b.qty_sold),
              unit_price: UtilitiesModule.divide(amount_std, b.qty_sold),
              amount_net: amount_net,
              unit_price_std: unit_price_std,
              unit_discount: unit_discount,
              unit_price_net: unit_price_net
            }
          )
          return b;
        }),
        toArray(),
        map(c => {
          a.data = c;
          return a;
        })
      ))
    ).subscribe(resolved => {
      console.log(resolved);
      this.viewColFacade.loadSuccess(resolved);
      this.totalRecords = resolved.data.length;
      this.rowData = [...this.rowData, ...resolved.data];
      this.gridApi.setRowData(this.rowData);
      this.viewColFacade.selectRowDataByFC(this.rowData);
      this.viewColFacade.selectTotalRecordsByFC(this.totalRecords);
    }, err => {
      console.error(err);
      let msg = err.status===403 ? ErrorMessages.CRUD_403_ERRORS.READ : err.message;
      this.toastr.error(
        msg,
        "AG Grid Error",
        {
          tapToDismiss: true,
          progressBar: true,
          timeOut: 1300
        }
      );
      this.viewColFacade.loadFailed(err);
    });
  };

  onToggle(e: boolean) {
    this.viewColFacade.toggleColumn(e);
  }

  onSearch(e: SearchQueryModel) {
    console.log("search", e);
    if (!e.isEmpty) {
      if (e.keyword && e.keyword.length > 0 && e.keyword.length < 3)  {
        this.toastr.error(
          'Search keyword must more than 2 characters.',
          'Keyword',
          {
            tapToDismiss: true,
            progressBar: true,
            timeOut: 1300
          }
        );
        return;
      }

      const inputModel = {} as SalesReportByItemCodeInputModel;
      inputModel.keyword = e.keyword;
      inputModel.date_from = '2022-01-01T00:00:00.000Z';
      inputModel.date_to = '2099-12-31T00:00:00.000Z';
      inputModel.branch_guids = this.branchGuids ?? [];
      if (e.queryString) {
        inputModel.keyword = UtilitiesModule.checkNull(e.queryString['itemCode'],'');
        inputModel.branch_guids = UtilitiesModule.checkNull(e.queryString['branch'],[]);
        inputModel.date_from = UtilitiesModule.checkNull(e.queryString['date']['from'],'2022-01-01T00:00:00.000Z');
        inputModel.date_to = UtilitiesModule.checkNull(e.queryString['date']['to'],'2099-12-31T00:00:00.000Z');
        inputModel.customer_guids = UtilitiesModule.checkNull(e.queryString['customer'],[]);
        inputModel.salesman_guids = UtilitiesModule.checkNull(e.queryString['salesman'],[]);
        inputModel.item_type = UtilitiesModule.checkNull(e.queryString['itemType'],[]);
        inputModel.item_status = [UtilitiesModule.checkNull(e.queryString['itemStatus'],[])];
        inputModel.item_category1_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel1'],[]);
        inputModel.item_category2_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel2'],[]);
        inputModel.item_category3_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel3'],[]);
        inputModel.item_category4_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel4'],[]);
        inputModel.item_category5_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel5'],[]);
        inputModel.item_category6_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel6'],[]);
        inputModel.item_category7_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel7'],[]);
        inputModel.item_category8_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel8'],[]);
        inputModel.item_category9_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel9'],[]);
        inputModel.item_category10_guids =UtilitiesModule.checkNull( e.queryString['itemCategoryLevel10'],[]);
      
        this.salesCost = UtilitiesModule.checkNull(e.queryString['calculateBaseOn'],'cost_ma');
      }
      inputModel.server_doc_type = ['INTERNAL_SALES_CASHBILL','INTERNAL_SALES_INVOICE','INTERNAL_SALES_RETURN'];
      this.createData(inputModel);

      const date_from = UtilitiesModule.dateFormatter(inputModel.date_from);
      const date_to = UtilitiesModule.dateFormatter(inputModel.date_to);     
      // Set branch names asynchronously
      this.setBranchSubTitle(inputModel.branch_guids, date_from, date_to);
     }
    else {
      //this.createData();
    }
  }

  clear() {
    this.gridApi.setRowData(null);
    this.totalRecords = 0;
    this.rowData = [];
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

  setDataRowCache() {
    //console.log('set data row cache');
    this.gridApi.setRowData(this.rowData);
  }

  getSalesCost(model: SalesReportByItemCodeModel) {
    return model[this.salesCost + '_amount'];
  }

  calculateTotal(node, key) {
    if (node.group) {
      let totalCost = 0;
      const children = node.childrenAfterGroup;

      children.forEach((childNode) => {
        const childCost = this.calculateTotal(childNode, key);
        totalCost += childCost;
      });

      return totalCost;
    } else {
      const rowData = node.data;
      return rowData && rowData[key] ? rowData[key] : 0;
    }
  }

  getBaseAmt(data, value) {
    if (!value || value === 0) return 0;

    if (this.isForex(data)) {
      return value / (data?.base_doc_xrate || 1);
    }
    else {
      return value;
    }
  }

  isForex(data): boolean {
    return data?.doc_ccy && data?.base_doc_ccy && data?.doc_ccy !== data?.base_doc_ccy && data?.base_doc_xrate > 0;
  }

  setBranchSubTitle(guids: string[], dateFrom: string, dateTo: string) {
    const key = 'branch';

    this.sessionStore.select(SessionSelectors.selectDropDownRowData).pipe(take(1)).subscribe(resolved => {
      let rowData = resolved ? resolved[key] ? resolved[key] : [] : [];
      let branchText = "ALL BRANCHES";

      if (rowData.length > 0 && guids && guids.length > 0) {
        const branches = rowData.filter(row => guids.includes(row.guid));
        if (branches.length > 0) {
          branchText = branches.map(branch => `${branch.name} (${branch.code})`).join(', ');
        }
      }

      let subTitle = branchText;
      subTitle += "\n";
      subTitle += `${dateFrom} to ${dateTo}`;

      const statusBarComponent = this.gridApi.getStatusPanel('statusBarExportKey');
      statusBarComponent.setSubTitle(subTitle);
    });
  }
}
