export const menuItems = [
  {
    state: '',
    name: sessionStorage.getItem('tenantCode'),
    type: 'tenant',
    icon: 'https'
  },
  {
    state: 'sales-report-by-item-code',
    name: 'SR By Item Code',
    type: 'link',
    icon: 'view_list' // Represents list of items
  },
  {
    state: 'sales-report-by-document',
    name: 'SR By Document',
    type: 'link',
    icon: 'description' // Represents a document
  },
  {
    state: 'sales-report-by-finance-charges',
    name: 'SR By Finance Charges',
    type: 'link',
    icon: 'request_quote' // Finance-focused
  },
  {
    state: 'sales-report-by-daily-weekly-monthly',
    name: 'SR By Daily Weekly Monthly',
    type: 'link',
    icon: 'date_range' // Represents time periods/date ranges
  },
  {
    state: 'daily-sales-report-cashflow',
    name: 'Daily Cashflow Analysis',
    type: 'link',
    icon: 'stacked_line_chart' // For cashflow/graphs
  },
  {
    state: "hourly-sales-report",
    name: "Hourly SR",
    type: "link",
    icon: "schedule" // Represents hourly timing
  },
  {
    state: "daily-gp-by-salesman",
    name: "Daily GP By Salesman",
    type: "link",
    icon: "badge" // Represents a person/salesman
  },
  {
    state: "sales-report-by-salesman",
    name: "SR By Salesman",
    type: "link",
    icon: "supervisor_account" // Group of people
  },
  {
    state: "multi-branch-sales-purchase-collection",
    name: "Multi Branch Sales",
    type: "link",
    icon: "account_tree" // Multiple branches
  },
  {
    state: "daily-collection-summary",
    name: "Daily Summary",
    type: "link",
    icon: "summarize" // Summary document
  },
  {
    state: "receipt-with-credit-card",
    name: "Receipt with Credit Card",
    type: "link",
    icon: "credit_score" // Represents credit card receipt
  },
  {
    state: "collection-with-invoice-detail",
    name: "Collection Invoice Detail",
    type: "link",
    icon: "receipt_long"
  },
  {
    state: 'sales-report-by-serial-number',
    name: 'SR By Serial Number',
    type: 'link',
    icon: 'qr_code' // Serial number association
  },
  {
    state: 'sales-item-collection-invoice',
    name: 'Sales Item and Collection Invoice',
    type: 'link',
    icon: 'assignment' // General report
  },
  {
    state: 'sales-report-by-daily-weekly-monthly',
    name: 'SR By Daily Weekly Monthly',
    type: 'link',
    icon: 'date_range' // Represents time periods/date ranges
  },
  {
    state: 'sales-report-by-item-salesman-customer-category',
    name: 'SR By Item Salesman Customer Category',
    type: 'link',
    icon: 'group' // Represents grouping by multiple criteria
  }
];

export const settingItems = [
  {
    group: 'System Configuration',
    child: [
      {
        state: 'field-settings',
        name: 'Application Settings'
      },
      {
        state: 'default-selection',
        name: 'Default Selection'
      },
    ]
  }
]

export const personalizationItems = [
  {
    group: 'System Configuration',
    child: [
      // {
      //   state: 'field-settings',
      //   name: 'Field Settings'
      // },
      {
        state: 'personal-default-selection',
        name: 'Default Selection'
      },
    ]
  }
];
