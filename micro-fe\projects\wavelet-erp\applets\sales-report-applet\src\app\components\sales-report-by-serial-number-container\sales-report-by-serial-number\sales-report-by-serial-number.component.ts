// Angular Core
import { ChangeDetectionStrategy, Component, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';

// NgRx
import { Store } from "@ngrx/store";
import { ComponentStore } from '@ngrx/component-store';

// Third-party Libraries
import { GridOptions } from 'ag-grid-enterprise';
import { DocumentShortCodesClass, ErrorMessages } from 'blg-akaun-ts-lib';
import { ToastrService } from 'ngx-toastr';
import { Observable, from, of } from 'rxjs';
import { filter, map, mergeMap, switchMap, take, tap, toArray } from 'rxjs/operators';
import { SubSink } from 'subsink2';

// Session Controller
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionActions } from 'projects/shared-utilities/modules/session/session-controller/actions';

// Permissions
import { PermissionStates } from 'projects/shared-utilities/modules/permission/permission-controller';
import { UserPermInquirySelectors } from 'projects/shared-utilities/modules/permission/user-permissions-inquiry-controller/selectors';

// Shared Utilities
import { SearchQueryModel } from 'projects/shared-utilities/models/query.model';
import { CellClickHandlerComponent } from 'projects/shared-utilities/utilities/cell-click-handler.component';
import { UtilitiesModule } from 'projects/shared-utilities/utilities/utilities.module';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { AppConfig } from 'projects/shared-utilities/visa';

// Application Imports
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { SalesReportByItemCodeSearchModel } from '../../../models/advanced-search-models/sales-report-by-item-code-search.model';
import { SalesReportByItemCodeInputModel, SalesReportByItemCodeModel } from '../../../models/sales-report-by-item-code-model';
import { ApiService } from '../../../services/api-service';
import { MultilineCellRendererComponent } from '../../utilities/cell-renderer/multiline-cell-renderer.component';

interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
}

@Component({
  selector: 'app-sales-report-by-serial-number',
  templateUrl: './sales-report-by-serial-number.component.html',
  styleUrls: ['./sales-report-by-serial-number.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})

export class SalesReportBySerialNumberComponent extends ViewColumnComponent implements OnInit, OnDestroy {
  @ViewChild(CellClickHandlerComponent) cellClickHandler!: CellClickHandlerComponent;
  protected subs = new SubSink();

  compId = 'salesReportBySerialNumber';
  compName = 'Sales Report By Serial Number';
  protected readonly index = 0;
  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  protected localState: LocalState;

  readonly deactivateAdd$ = this.componentStore.select(state => state.deactivateAdd);
  readonly deactivateList$ = this.componentStore.select(state => state.deactivateList);

  readonly userPermissionTarget$ = this.permissionStore.select(
    UserPermInquirySelectors.selectUserPermInquiry
  );

  toggleColumn$: Observable<boolean>;
  searchModel = SalesReportByItemCodeSearchModel;

  showColumns = [
    { name: 'sales_cost', setting: 'HIDE_MA_COST', permission: 'SHOW_MA_COST' },
    { name: 'gp', setting: 'HIDE_GP', permission: 'SHOW_GP' },
    { name: 'gp_percentage', setting: 'HIDE_GP_PERCENTAGE', permission: 'SHOW_GP_PERCENTAGE' },
    { name: 'landed_ma_amount', setting: 'HIDE_MA_COST', permission: 'SHOW_MA_COST' },
    { name: 'gp_landed', setting: 'HIDE_GP', permission: 'SHOW_GP' },
    { name: 'gp_percentage_landed', setting: 'HIDE_GP_PERCENTAGE', permission: 'SHOW_GP_PERCENTAGE' },
  ]
  
  rowData = [];
  totalRecords = 0;
  salesCost = 'cost_ma';
  branchGuids = [];

  // api visa
  apiVisa = AppConfig.apiVisa;

  // initial grid state
  gridApi;
  gridColumnApi;
  
  gridOptions: GridOptions = {
    pagination: false,
    groupIncludeTotalFooter: true,
    onCellClicked: (event) => this.onCellClicked(event)
  };

  columnsDefs = [
    {
      headerName: 'Branch',
      field: 'branch_code',
      type: 'textColumn'
    },
    {
      headerName: 'Location',
      field: 'location_code',
      type: 'textColumn'
    },
    {
      headerName: 'Customer Code',
      field: 'customer_code',
      type: 'textColumn'
    },
    {
      headerName: 'Customer Name',
      field: 'customer_name',
      type: 'textColumn'
    },
    {
      headerName: 'Salesman',
      field: 'salesman',
      type: 'textColumn'
    },
    {
      headerName: 'Salesman Code',
      field: 'salesman_code',
      type: 'textColumn'
    },
    {
      headerName: 'Item Code',
      field: 'item_code',
      type: 'textColumn'
    },
    {
      headerName: 'Item Name',
      field: 'item_name',
      type: 'textColumn'
    },
    {
      headerName: 'Type',
      field: 'txn_type',
      type: 'textColumn'
    },
    {
      headerName: 'UOM',
      field: 'uom',
      type: 'textColumn'
    },
    {
      headerName: 'Date',
      field: 'date_txn',
      type: 'dateColumn'
    },
    {
      headerName: 'Time',
      field: '',
      type: 'textColumn',
      cellRenderer: (params) => {
        if (params.node.group) return '';
        const dateString = params.date_txn;
        const moment = require("moment");
        const momentObject = moment(dateString);
        const time = momentObject.format("hh:mm:ss");
        return time;
      }
    },
    {
      headerName: 'Doc Type',
      field: 'doc_type',
      type: 'textColumn'
    },
    {
      headerName: 'Doc No',
      field: 'doc_no',
      type: 'textColumn',
      cellStyle: { textAlign: 'left', cursor: 'pointer', color: 'blue' },
      tooltipValueGetter: () => 'Click to view',
      onCellClicked: (e: any) => this.onCellClicked(e)
    },
    {
      headerName: 'Remarks',
      field: 'doc_remarks',
      type: 'textColumn'
    },
    {
      headerName: 'Qty',
      field: 'qty_sold',
      type: 'integerColumn',
    },
    {
      headerName: 'Unit Price',
      field: 'unit_price',
      cellRenderer: MultilineCellRendererComponent,
      autoHeight: true,
      type: 'decimalColumn',
      aggFunc: '',
      valueFormatter: params => params.node.group ? '' : params.value ? UtilitiesModule.currencyFormatter(params.value) : '0.00',
    },
    {
      headerName: 'Unit Cost',
      field: 'unit_cost',
      cellRenderer: MultilineCellRendererComponent,
      autoHeight: true,
      type: 'decimalColumn',
      aggFunc: '',
      valueFormatter: params => params.node.group ? '' : params.value ? UtilitiesModule.currencyFormatter(params.value) : '0.00',
    },
    {
      headerName: 'Gross Amount',
      field: 'amount_std',
      cellRenderer: MultilineCellRendererComponent,
      autoHeight: true,
      type: 'decimalColumn',
    },
    {
      headerName: 'Amount Disc',
      field: 'amount_discount',
      cellRenderer: MultilineCellRendererComponent,
      autoHeight: true,
      type: 'decimalColumn',
    },
    {
      headerName: 'Amount Net',
      field: 'amount_net',
      cellRenderer: MultilineCellRendererComponent,
      autoHeight: true,
      type: 'decimalColumn',
    },
    {
      headerName: 'Amount Tax',
      field: 'amount_tax_gst',
      cellRenderer: MultilineCellRendererComponent,
      autoHeight: true,
      type: 'decimalColumn'
    },
    {
      headerName: 'Amount Txn',
      field: 'amount_txn',
      cellRenderer: MultilineCellRendererComponent,
      autoHeight: true,
      type: 'decimalColumn'
    },
    {
      headerName: 'Apportion Disc Ratio',
      field: 'apt_weight_disc',
      type: 'decimalColumn',
      hide: true
    },
    {
      headerName: 'Apportion Amt Disc',
      field: 'apt_amt_discount',
      type: 'decimalColumn',
      hide: true
    },
    {
      headerName: 'Disc + App Disc',
      field: '',
      type: 'decimalColumn',
      hide: true,
      cellRenderer: (params) => {
        let newDisc = 0;
        if(params.data){
          const amountDiscount = params.data.amount_discount || 0;
          const aptAmtDiscount = params.data.apt_amt_discount || 0;
          newDisc = amountDiscount + aptAmtDiscount;
        }

       return newDisc.toFixed(2);
      }
    },
    {
      headerName: 'Amt Txn after Apportion Disc',
      field: '',
      type: 'decimalColumn',
      hide: true,
      cellRenderer: (params) => {
        let value = 0;
        if(params.data){
          const amountStd = params.data.amount_std || 0;
          const amountDiscount = params.data.amount_discount || 0;
          const aptAmtDiscount = params.data.apt_amt_discount || 0;
          const newDisc = amountDiscount + aptAmtDiscount;
          value = amountStd - newDisc;
        }

        return value.toFixed(2);
       }
    },
    {
      headerName: 'Cost Amt',
      field: 'sales_cost',
      cellRenderer: MultilineCellRendererComponent,
      autoHeight: true,
      type: 'decimalColumn'
    },
    {
      headerName: 'GP',
      field: 'gp',
      cellRenderer: MultilineCellRendererComponent,
      autoHeight: true,
      type: 'decimalColumn'
    },
    {
      headerName: 'GP%',
      field: 'gp_percentage',
      type: 'decimalColumn',
      aggFunc: '',
      valueGetter: (params) => {
        const cost = this.calculateTotal(params.node, 'gp');
        const salesAmount = this.calculateTotal(params.node, 'amount_net');

        if (cost && salesAmount && salesAmount !== 0) {
          return (cost / salesAmount) * 100;
        }
        return 0;
      },
    },
    {
      headerName: 'Category 1',
      field: 'category1_code',
      rowGroup: false, hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Category 2',
      field: 'category2_code',
      rowGroup: false, hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Category 3',
      field: 'category3_code',
      rowGroup: false, hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Category 4',
      field: 'category4_code',
      rowGroup: false, hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Category 5',
      field: 'category5_code',
      rowGroup: false, hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Category 6',
      field: 'category6_code',
      rowGroup: false, hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Category 7',
      field: 'category7_code',
      rowGroup: false, hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Category 8',
      field: 'category8_code',
      rowGroup: false, hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Category 9',
      field: 'category9_code',
      rowGroup: false, hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Category 10',
      field: 'category10_code',
      rowGroup: false, hide: true,
      type: 'textColumn'
    },
    {
      headerName: "Client Doc 1",
      field: "client_doc_1",
      type: "textColumn",
      hide: true,
    },
    {
      headerName: "Client Doc 2",
      field: "client_doc_2",
      type: "textColumn",
      hide: true,
    },
    {
      headerName: "Client Doc 3",
      field: "client_doc_3",
      type: "textColumn",
      hide: true,
    },
    {
      headerName: "Client Doc 4",
      field: "client_doc_4",
      type: "textColumn",
      hide: true,
    },
    {
      headerName: "Client Doc 5",
      field: "client_doc_5",
      type: "textColumn",
      hide: true,
    },
    {
      headerName: 'Phone',
      field: 'phone',
      rowGroup: false, hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Email',
      field: 'notification_email',
      rowGroup: false, hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Serial #',
      field: 'serial',
      rowGroup: false, hide: false,
      type: 'textColumn'
    },
    {
      headerName: 'Race',
      field: 'demographic_race',
      rowGroup: false, hide: true,
      type: 'textColumn'
    },
  ];

  readPermissionDefintion = {
    branch: 'API_TNT_DM_ERP_SALES_REPORT_BY_DOCUMENT_READ'
  }
  constructor(
    protected viewColFacade: ViewColumnFacade,
    protected apiService: ApiService,
    protected toastr: ToastrService,
    protected readonly componentStore: ComponentStore<LocalState>,
    private readonly permissionStore: Store<PermissionStates>,
    protected readonly sessionStore: Store<SessionStates>) {
    super();
  }

  ngOnInit() {
    // Initialize branch GUIDs based on permissions
    this.branchGuids = UtilitiesModule.getTargetsByPermission(this.userPermissionTarget$,
      this.readPermissionDefintion.branch, 'bl_fi_mst_branch'
    );

    this.toggleColumn$ = this.viewColFacade.toggleColumn$;
    this.subs.sink = this.viewColFacade.rowDataBySN$.subscribe(rowData => this.rowData = rowData);
    this.subs.sink = this.viewColFacade.totalRecordsBySN$.subscribe(totalRecords => this.totalRecords = totalRecords);
    this.subs.sink = this.localState$.subscribe(a => {
      this.localState = a;
      this.componentStore.setState(a);
    });
  }

  onNext() {
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState, deactivateAdd: true, deactivateList: false
    });
    this.viewColFacade.onNextAndReset(this.index, 1);
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.closeToolPanel();

    if (this.rowData.length > 0) {
      this.setDataRowCache();
    }
    else
    {
      //this.createData();
    }

    const statusBarComponent = this.gridApi.getStatusPanel('statusBarExportKey');
    statusBarComponent.setTitle(this.compName);
    statusBarComponent.setFilename(this.compName);
    statusBarComponent.setHideGroupColumn(true);
  }

  createData(inputModel?: SalesReportByItemCodeInputModel) {
    console.log("on create data....");
    this.clear();
    this.viewColFacade.loadInit(this.gridApi);
    this.subs.sink = this.apiService.getSalesReportByDocument(inputModel, this.apiVisa).pipe(
      mergeMap(a => from(a.data).pipe(
        map(b => {
          const sales_cost = this.getBaseAmt(b, this.getSalesCost(b));
          const amount_std = this.getBaseAmt(b, b.amount_std);
          const amount_discount = this.getBaseAmt(b, b.amount_discount);
          const amount_net = this.getBaseAmt(b, b.amount_net);
          const amount_tax = this.getBaseAmt(b, b.amount_tax_gst);
          const amount_txn = this.getBaseAmt(b, b.amount_txn);

          const serial_list = b.serial_no && b.serial_no.serialNumbers ? b.serial_no.serialNumbers : [];
          Object.assign(b,
            {
              doc_type: DocumentShortCodesClass.serverDocTypeToShortCodeMapper(b.server_doc_type),
              doc_no: b.server_doc_1,
              sales_cost: sales_cost,
              unit_cost: UtilitiesModule.divide(sales_cost, b.qty_sold),
              unit_price: UtilitiesModule.divide(amount_std, b.qty_sold),
              gp: amount_txn - sales_cost,
              gp_percentage: Number(amount_txn) !== 0 ? (amount_txn - sales_cost) * 100 / amount_txn : 0,
              serial_list: serial_list,
              amount_std : amount_std,
              amount_discount: amount_discount,
              amount_net: amount_net,
              amount_tax: amount_tax,
              amount_txn: amount_txn
            }
          )
          return b;
        }),
        toArray(),
        map(c => {
          a.data = c;
          return a;
        })
      ))
    ).subscribe(resolved => {
      console.log(resolved);
      this.viewColFacade.loadSuccess(resolved);
      this.totalRecords = resolved.data.length;
      this.rowData = [...this.rowData, ...resolved.data];
      this.rowData = this.transformedData(this.rowData);
      this.gridApi.setRowData(this.rowData);
      this.viewColFacade.selectRowDataBySN(this.rowData);
      this.viewColFacade.selectTotalRecordsBySN(this.totalRecords);
    }, err => {
      console.error(err);
      let msg = err.status===403 ? ErrorMessages.CRUD_403_ERRORS.READ : err.message;
      this.toastr.error(
        msg,
        "AG Gird Error",
        {
          tapToDismiss: true,
          progressBar: true,
          timeOut: 1300
        }
      );
      this.viewColFacade.loadFailed(err);
    });
  };

  onToggle(e: boolean) {
    this.viewColFacade.toggleColumn(e);
  }

  onSearch(e: SearchQueryModel) {
    console.log("search", e);
    if (!e.isEmpty) {
      if (e.keyword && e.keyword.length > 0 && e.keyword.length < 3)  {
        this.toastr.error(
          'Search keyword must more than 2 characters.',
          'Keyword',
          {
            tapToDismiss: true,
            progressBar: true,
            timeOut: 1300
          }
        );
        return;
      }

      const inputModel = {} as SalesReportByItemCodeInputModel;
      inputModel.keyword = e.keyword;
      inputModel.date_from = '2022-01-01T00:00:00.000Z';
      inputModel.date_to = '2099-12-31T00:00:00.000Z';
      inputModel.branch_guids = this.branchGuids ?? [];
      if (e.queryString) {
        inputModel.keyword = UtilitiesModule.checkNull(e.queryString['itemCode'],'');
        inputModel.branch_guids = UtilitiesModule.checkNull(e.queryString['branch'],[]);
        inputModel.date_from = UtilitiesModule.checkNull(e.queryString['date']['from'],'2022-01-01T00:00:00.000Z');
        inputModel.date_to = UtilitiesModule.checkNull(e.queryString['date']['to'],'2099-12-31T00:00:00.000Z');
        inputModel.customer_guids = UtilitiesModule.checkNull(e.queryString['customer'],[]);
        inputModel.salesman_guids = UtilitiesModule.checkNull(e.queryString['salesman'],[]);
        inputModel.item_type = UtilitiesModule.checkNull(e.queryString['itemType'],[]);
        inputModel.item_status = [UtilitiesModule.checkNull(e.queryString['itemStatus'],[])];
        inputModel.item_category1_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel1'],[]);
        inputModel.item_category2_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel2'],[]);
        inputModel.item_category3_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel3'],[]);
        inputModel.item_category4_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel4'],[]);
        inputModel.item_category5_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel5'],[]);
        inputModel.item_category6_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel6'],[]);
        inputModel.item_category7_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel7'],[]);
        inputModel.item_category8_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel8'],[]);
        inputModel.item_category9_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel9'],[]);
        inputModel.item_category10_guids =UtilitiesModule.checkNull( e.queryString['itemCategoryLevel10'],[]);
      
        this.salesCost = UtilitiesModule.checkNull(e.queryString['calculateBaseOn'],'cost_ma');
      }
      inputModel.server_doc_type = ['INTERNAL_SALES_CASHBILL','INTERNAL_SALES_INVOICE','INTERNAL_SALES_RETURN'];
      this.createData(inputModel);

      const date_from = UtilitiesModule.dateFormatter(inputModel.date_from);
      const date_to = UtilitiesModule.dateFormatter(inputModel.date_to);

      // Set branch names asynchronously
      this.setBranchSubTitle(inputModel.branch_guids, date_from, date_to);
    }
    else {
      //this.createData();
    }
  }

  clear() {
    this.gridApi.setRowData([]);
    this.totalRecords = 0;
    this.rowData = [];
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

  setDataRowCache() {
    //console.log('set data row cache');
    this.gridApi.setRowData(this.rowData);
  }

  getSalesCost(model: SalesReportByItemCodeModel) {
    return model[this.salesCost + '_amount'];
  }

  calculateTotal(node, key) {
    if (node.group) {
      let totalCost = 0;
      const children = node.childrenAfterGroup;

      children.forEach((childNode) => {
        const childCost = this.calculateTotal(childNode, key);
        totalCost += childCost;
      });

      return totalCost;
    } else {
      const rowData = node.data;
      return rowData && rowData[key] ? rowData[key] : 0;
    }
  }

  onCellClicked(e: any) {
    if (this.cellClickHandler) {
      this.cellClickHandler.onCellClicked(null,e.data.doc_no, e.data.server_doc_type);
    }
  }

  transformedData(dataRow) {
    return dataRow.reduce((acc, row) => {
      if (row.serial_list.length > 0) {
        row.serial_list.forEach(serial => {
          acc.push({
            ...row,
            qty: '1',
            serial: serial
          });
        });
      }
      return acc;
    }, []);
  }

  getBaseAmt(data, value) {
    if (!value || value === 0) return 0;

    if (this.isForex(data)) {
      return value / (data?.base_doc_xrate || 1);
    }
    else {
      return value;
    }
  }

  isForex(data): boolean {
    return data?.doc_ccy && data?.base_doc_ccy && data?.doc_ccy !== data?.base_doc_ccy && data?.base_doc_xrate > 0;
  }

  setBranchSubTitle(guids: string[], dateFrom: string, dateTo: string) {
    const key = 'branch';

    this.sessionStore.select(SessionSelectors.selectDropDownRowData).pipe(take(1)).subscribe(resolved => {
      let rowData = resolved ? resolved[key] ? resolved[key] : [] : [];
      let branchText = "ALL BRANCHES";

      if (rowData.length > 0 && guids && guids.length > 0) {
        const branches = rowData.filter(row => guids.includes(row.guid));
        if (branches.length > 0) {
          branchText = branches.map(branch => `${branch.name} (${branch.code})`).join(', ');
        }
      }

      let subTitle = branchText;
      subTitle += "\n";
      subTitle += `${dateFrom} to ${dateTo}`;

      const statusBarComponent = this.gridApi.getStatusPanel('statusBarExportKey');
      statusBarComponent.setSubTitle(subTitle);
    });
  }
}
