// Angular Core
import { ChangeDetectionStrategy, Component, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';

// Angular Material
import { MatDialog } from '@angular/material/dialog';

// NgRx
import { ComponentStore } from '@ngrx/component-store';
import { Store } from '@ngrx/store';

// Third-party Libraries
import { GridOptions } from 'ag-grid-enterprise';
import { DocumentShortCodesClass, ErrorMessages, GenericDocAllService } from 'blg-akaun-ts-lib';
import { ToastrService } from 'ngx-toastr';
import { Observable, from } from 'rxjs';
import { concatMap, map, mergeMap, take, toArray } from 'rxjs/operators';
import { SubSink } from 'subsink2';

// Shared Utilities - Dialogues
import { AkaunGenericDocViewDialogComponent } from 'projects/shared-utilities/dialogues/akaun-generic-doc-view-dialog/akaun-generic-doc-view-dialog.component';

// Shared Utilities - Models
import { SearchQueryModel } from 'projects/shared-utilities/models/query.model';

// Shared Utilities - Permissions
import { PermissionStates } from 'projects/shared-utilities/modules/permission/permission-controller';
import { UserPermInquirySelectors } from 'projects/shared-utilities/modules/permission/user-permissions-inquiry-controller/selectors';

// Shared Utilities - Session
import { SessionActions } from 'projects/shared-utilities/modules/session/session-controller/actions';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';

// Shared Utilities - Components & Utilities
import { CellClickHandlerComponent } from 'projects/shared-utilities/utilities/cell-click-handler.component';
import { UtilitiesModule } from 'projects/shared-utilities/utilities/utilities.module';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { AppConfig } from 'projects/shared-utilities/visa';

// Application Imports
import { ApiService } from '../../../services/api-service';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { CollectionWithInvoiceDetailSearchModel } from '../../../models/advanced-search-models/collection-with-invoice-detail-search.model';
import { SalesReportByItemCodeInputModel, SalesReportByItemCodeModel } from '../../../models/sales-report-by-item-code-model';

interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
}

@Component({
  selector: 'app-collection-with-invoice-detail',
  templateUrl: './collection-with-invoice-detail.component.html',
  styleUrls: ['./collection-with-invoice-detail.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})

export class CollectionWithInvoiceDetailComponent extends ViewColumnComponent implements OnInit, OnDestroy {
  @ViewChild(CellClickHandlerComponent) cellClickHandler!: CellClickHandlerComponent;
  protected subs = new SubSink();

  compId = 'collectionWithInvoiceDetails';
  compName = 'Collection with Invoice Details';
  protected readonly index = 0;
  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  protected localState: LocalState;

  readonly deactivateAdd$ = this.componentStore.select(state => state.deactivateAdd);
  readonly deactivateList$ = this.componentStore.select(state => state.deactivateList);

  toggleColumn$: Observable<boolean>;
  searchModel = CollectionWithInvoiceDetailSearchModel;

  showColumns = [
    { name: 'sales_cost', setting: 'HIDE_MA_COST', permission: 'SHOW_MA_COST' },
    { name: 'gp', setting: 'HIDE_GP', permission: 'SHOW_GP' },
    { name: 'gp_percentage', setting: 'HIDE_GP_PERCENTAGE', permission: 'SHOW_GP_PERCENTAGE' },
    { name: 'landed_ma_amount', setting: 'HIDE_MA_COST', permission: 'SHOW_MA_COST' },
    { name: 'gp_landed', setting: 'HIDE_GP', permission: 'SHOW_GP' },
    { name: 'gp_percentage_landed', setting: 'HIDE_GP_PERCENTAGE', permission: 'SHOW_GP_PERCENTAGE' },
  ]

  rowData = [];
  totalRecords = 0;
  salesCost = 'cost_ma';

  // Permission and Branch properties
  branchGuids = [];
  readonly userPermissionTarget$ = this.permissionStore.select(
    UserPermInquirySelectors.selectUserPermInquiry
  );

  // api visa
  apiVisa = AppConfig.apiVisa;

  // initial grid state
  gridApi;
  gridColumnApi;

  gridOptions: GridOptions = {
    pagination: false,
    groupIncludeTotalFooter: true,
    onCellClicked: (event) => this.onCellClicked(event)
  };

  columnsDefs = [];

  readPermissionDefintion = {
    branch: 'API_TNT_DM_ERP_COLLECTION_INVOICE_DETAILS_READ'
  }

  constructor(
    protected viewColFacade: ViewColumnFacade,
    protected apiService: ApiService,
    protected toastr: ToastrService,
    protected readonly componentStore: ComponentStore<LocalState>,
    protected dialog: MatDialog,
    private genDocService: GenericDocAllService,
    private readonly permissionStore: Store<PermissionStates>,
    private readonly sessionStore: Store<SessionStates>,) {
    super();
  }

  ngOnInit() {
    // Initialize branch GUIDs based on permissions
    this.branchGuids = UtilitiesModule.getTargetsByPermission(this.userPermissionTarget$,
      this.readPermissionDefintion.branch, 'bl_fi_mst_branch'
    );

    this.columnsDefs = this.createDynamicColumnDefs();
    this.toggleColumn$ = this.viewColFacade.toggleColumn$;
    this.subs.sink = this.viewColFacade.rowDataByCID$.subscribe(rowData => this.rowData = rowData);
    this.subs.sink = this.viewColFacade.totalRecords$.subscribe(totalRecords => this.totalRecords = totalRecords);
    this.subs.sink = this.localState$.subscribe(a => {
      this.localState = a;
      this.componentStore.setState(a);
    });
  }


  createDynamicColumnDefs() {
    const invoiceColumns = [
      {
        headerName: 'Doc Short Code',
        field: 'docShortCode',
        type: 'textColumn'
      },
      {
        headerName: 'Document No',
        field: 'server_doc_1',
        type: 'textColumn',
        cellStyle: { textAlign: 'left', cursor: 'pointer', color: 'blue' },
        tooltipValueGetter: () => 'Click to view',
        cellRenderer: (params) => {
          return `<span class="clickable-cell">${params.value}</span>`;
        }
      },
      {
        headerName: 'Date',
        field: 'date_txn',
        type: 'dateColumn'
      },
      {
        headerName: 'Reference',
        field: 'doc_reference',
        type: 'textColumn'
      },
      {
        headerName: 'Amount Before Tax',
        field: 'amount_net',
        type: 'decimalColumn'
      },
      {
        headerName: 'Amount After Tax',
        field: 'amount_txn',
        type: 'decimalColumn'
      },
      {
        headerName: 'Balance',
        field: 'arap_bal',
        type: 'decimalColumn'
      }
    ];

    const invoiceItemColumns = [
      {
        headerName: 'Item Code',
        field: 'item_code',
        type: 'textColumn'
      },
      {
        headerName: 'Qty',
        field: 'quantity_base',
        type: 'integerColumn'
      },
      {
        headerName: 'Unit Price',
        field: 'unit_price_std',
        type: 'decimalColumn'
      },
      {
        headerName: 'Amount Before Tax',
        field: 'line_amount_net',
        type: 'decimalColumn'
      },
      {
        headerName: 'Amount After Tax',
        field: 'line_amount_txn',
        type: 'decimalColumn'
      },
      {
        headerName: 'Category 1',
        field: 'category1_code',
        rowGroup: false, hide: true,
        type: 'textColumn'
      },
      {
        headerName: 'Category 2',
        field: 'category2_code',
        rowGroup: false, hide: true,
        type: 'textColumn'
      },
      {
        headerName: 'Category 3',
        field: 'category3_code',
        rowGroup: false, hide: true,
        type: 'textColumn'
      },
      {
        headerName: 'Category 4',
        field: 'category4_code',
        rowGroup: false, hide: true,
        type: 'textColumn'
      },
      {
        headerName: 'Category 5',
        field: 'category5_code',
        rowGroup: false, hide: true,
        type: 'textColumn'
      },
      {
        headerName: 'Category 6',
        field: 'category6_code',
        rowGroup: false, hide: true,
        type: 'textColumn'
      },
      {
        headerName: 'Category 7',
        field: 'category7_code',
        rowGroup: false, hide: true,
        type: 'textColumn'
      },
      {
        headerName: 'Category 8',
        field: 'category8_code',
        rowGroup: false, hide: true,
        type: 'textColumn'
      },
      {
        headerName: 'Category 9',
        field: 'category9_code',
        rowGroup: false, hide: true,
        type: 'textColumn'
      },
      {
        headerName: 'Category 10',
        field: 'category10_code',
        rowGroup: false, hide: true,
        type: 'textColumn'
      },
    ];

    const contraColumns = [
      {
        headerName: 'Contra Doc Short Code',
        field: 'docShortCodeContra',
        type: 'textColumn'
      },
      {
        headerName: 'Contra Doc No',
        field: 'contra_server_doc_1',
        type: 'textColumn'
      },
      {
        headerName: 'Contra Date Txn',
        field: 'contra_date_txn',
        type: 'dateColumn'
      },
      {
        headerName: 'Amount Contra',
        field: 'amount_contra',
        type: 'decimalColumn'
      },
      {
        headerName: 'Settlement Method',
        field: 'settlement_code',
        type: 'textColumn'
      }
    ];

    // Empty headers for Salesman and Customer
    const salesmanColumn = {
      headerName: 'Salesman',
      field: 'sales_entity_hdr_name',
      type: 'textColumn'
    };
    const salesmanCodeColumn = {
      headerName: 'Salesman Code',
      field: 'sales_entity_hdr_code',
      type: 'textColumn'
    };
    const customerNoColumn = {
      headerName: 'Customer',
      field: 'customer_name',
      type: 'textColumn'
    }
    // Combine all columns into one array
    return [
      salesmanColumn,
      salesmanCodeColumn,
      customerNoColumn,
      { headerName: 'Invoice', children: invoiceColumns },
      { headerName: 'Invoice Item', children: invoiceItemColumns },
      { headerName: 'Contra', children: contraColumns },
    ];
  }

  onNext() {
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState, deactivateAdd: true, deactivateList: false
    });
    this.viewColFacade.onNextAndReset(this.index, 1);
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.closeToolPanel();

    if (this.rowData.length > 0) {
      this.setDataRowCache();
    }
    else
    {
      //this.createData();
    }

    const statusBarComponent = this.gridApi.getStatusPanel('statusBarExportKey');
    statusBarComponent.setTitle(this.compName);
    statusBarComponent.setFilename(this.compName);
    statusBarComponent.setHideGroupColumn(true);
  }

  createData(inputModel?: SalesReportByItemCodeInputModel) {
    this.clear();
    this.viewColFacade.loadInit(this.gridApi);

    this.subs.sink = this.apiService.getCollectionWithInvoiceDetail(inputModel, this.apiVisa).pipe(
        mergeMap(a => from(a.data).pipe(
            mergeMap(b => {
                Object.assign(b, {
                    docShortCode: DocumentShortCodesClass.serverDocTypeToShortCodeMapper(b['server_doc_type']),
                });

                const lineItems = b.line_items || [];
                const contraItems = b.contra_json_array || [];

                const maxLength = Math.max(lineItems.length, contraItems.length);
                const combinedData = Array.from({ length: maxLength }, (_, index) => {
                    const lineItem = lineItems[index] || {};  // If no more line items, use empty object
                    const contraItem = contraItems[index] || {};  // If no more contra items, use empty object

                    return {
                        amount_net: b.amount_net,
                        sales_entity_hdr_name: b.sales_entity_hdr_name,
                        sales_entity_hdr_code: b.sales_entity_hdr_code,
                        amount_txn: b.amount_txn,
                        doc_reference: b.doc_reference,
                        server_doc_type: b.server_doc_type,
                        date_txn: b.date_txn,
                        customer_name: b.customer_name,
                        server_doc_1: b.server_doc_1,
                        arap_bal: b.arap_bal,
                        docShortCode: b.docShortCode,

                        // Line
                        item_code: lineItem.item_code || null,
                        quantity_base: lineItem.quantity_base || null,
                        quantity_signum: lineItem.quantity_signum || null,
                        line_amount_std: lineItem.line_amount_std || null,
                        line_amount_txn: lineItem.line_amount_txn || null,
                        line_amount_net: lineItem.line_amount_net || null,
                        line_amount_tax_gst: lineItem.line_amount_tax_gst || null,
                        unit_price_std: lineItem.unit_price_std || null,
                        category1_code: lineItem.category1_code || null,
                        category2_code: lineItem.category2_code || null,
                        category3_code: lineItem.category3_code || null,
                        category4_code: lineItem.category4_code || null,
                        category5_code: lineItem.category5_code || null,
                        category6_code: lineItem.category6_code || null,
                        category7_code: lineItem.category7_code || null,
                        category8_code: lineItem.category8_code || null,
                        category9_code: lineItem.category9_code || null,
                        category10_code: lineItem.category10_code || null,


                        // Contra
                        contra_server_doc_1: contraItem.contra_server_doc_1 || null,
                        contra_server_doc_type: contraItem.contra_server_doc_type || null,
                        contra_date_txn: contraItem.contra_date_txn || null,
                        contra_doc_remarks: contraItem.contra_doc_remarks || null,
                        contra_arap_bal: contraItem.contra_arap_bal || null,
                        amount_contra: contraItem.amount_contra || null,
                        settlement_code: contraItem.settlement_code || null,
                        docShortCodeContra: DocumentShortCodesClass.serverDocTypeToShortCodeMapper(contraItem.contra_server_doc_type )
                    };
                });

                // Return an observable from the combined data array
                return from(combinedData);
            }),
            toArray(), // Gather all transformed data into a single array
            map(c => {
                a.data = c; // Update the data array with the transformed structure
                return a;
            })
        ))
    ).subscribe(resolved => {
        this.viewColFacade.loadSuccess(resolved);

        this.rowData = [...this.rowData, ...resolved.data];
        console.log('this.rowData', this.rowData);
        this.gridApi.setRowData(this.rowData);
        this.viewColFacade.selectRowDataByCID(this.rowData);
        this.viewColFacade.selectTotalRecords(this.totalRecords);
    }, err => {
        console.error(err);
        let msg = err.status===403 ? ErrorMessages.CRUD_403_ERRORS.READ : err.message;
        this.toastr.error(
            msg,
            "AG Grid Error",
            {
                tapToDismiss: true,
                progressBar: true,
                timeOut: 1300
            }
        );
        this.viewColFacade.loadFailed(err);
    });
}


  createData2(inputModel?: SalesReportByItemCodeInputModel) {
    this.clear();
    this.viewColFacade.loadInit(this.gridApi);
    this.subs.sink = this.apiService.getCollectionWithInvoiceDetail(inputModel, this.apiVisa).pipe(
      mergeMap(a => from(a.data).pipe(
        map(b => {
          Object.assign(b,
            {
              docShortCode: DocumentShortCodesClass.serverDocTypeToShortCodeMapper(b['server_doc_type']),
              docShortCodeContra: DocumentShortCodesClass.serverDocTypeToShortCodeMapper(b['contra_server_doc_type'])
            }
          )
          return b;
        }),
        toArray(),
        map(c => {
          a.data = c;
          return a;
        })
      ))
    ).subscribe(resolved => {
      console.log(resolved);
      this.viewColFacade.loadSuccess(resolved);
      this.totalRecords = resolved.data.length;
      this.rowData = [...this.rowData, ...resolved.data];
      this.gridApi.setRowData(this.rowData);
      this.viewColFacade.selectRowDataByCID(this.rowData);
      this.viewColFacade.selectTotalRecords(this.totalRecords);
    }, err => {
      console.error(err);
      let msg = err.status===403 ? ErrorMessages.CRUD_403_ERRORS.READ : err.message;
      this.toastr.error(
        msg,
        "AG Gird Error",
        {
          tapToDismiss: true,
          progressBar: true,
          timeOut: 1300
        }
      );
      this.viewColFacade.loadFailed(err);
    });
  };

  onToggle(e: boolean) {
    this.viewColFacade.toggleColumn(e);
  }

  onSearch(e: SearchQueryModel) {
    console.log("search", e);
    if (!e.isEmpty) {
      if (e.keyword && e.keyword.length > 0 && e.keyword.length < 3)  {
        this.toastr.error(
          'Search keyword must more than 2 characters.',
          'Keyword',
          {
            tapToDismiss: true,
            progressBar: true,
            timeOut: 1300
          }
        );
        return;
      }

      const inputModel = {} as SalesReportByItemCodeInputModel;
      inputModel.keyword = e.keyword;
      inputModel.date_from = '2022-01-01T00:00:00.000Z';
      inputModel.date_to = '2099-12-31T00:00:00.000Z';

      // Filter by branch permissions
      inputModel.branch_guids = this.branchGuids??[];

      if (e.queryString) {
        inputModel.keyword = UtilitiesModule.checkNull(e.queryString['itemCode'],'');
        inputModel.branch_guids = UtilitiesModule.checkNull(e.queryString['branch'],[]);
        inputModel.date_from = UtilitiesModule.checkNull(e.queryString['date']['from'],'2022-01-01T00:00:00.000Z');
        inputModel.date_to = UtilitiesModule.checkNull(e.queryString['date']['to'],'2099-12-31T00:00:00.000Z');
        inputModel.customer_guids = UtilitiesModule.checkNull(e.queryString['customer'],[]);
        inputModel.salesman_guids = UtilitiesModule.checkNull(e.queryString['salesman'],[]);
        inputModel.item_type = UtilitiesModule.checkNull(e.queryString['itemType'],[]);
        inputModel.item_status = UtilitiesModule.checkNull(e.queryString['itemStatus'],[]);
        inputModel.item_category1_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel1'],[]);
        inputModel.item_category2_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel2'],[]);
        inputModel.item_category3_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel3'],[]);
        inputModel.item_category4_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel4'],[]);
        inputModel.item_category5_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel5'],[]);
        inputModel.item_category6_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel6'],[]);
        inputModel.item_category7_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel7'],[]);
        inputModel.item_category8_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel8'],[]);
        inputModel.item_category9_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel9'],[]);
        inputModel.item_category10_guids =UtilitiesModule.checkNull( e.queryString['itemCategoryLevel10'],[]);

        this.salesCost = UtilitiesModule.checkNull(e.queryString['calculateBaseOn'],'cost_ma');
      }
      inputModel.server_doc_type = ['INTERNAL_SALES_CASHBILL','INTERNAL_SALES_INVOICE','INTERNAL_SALES_RETURN'];
      this.createData(inputModel);

      const date_from = UtilitiesModule.dateFormatter(inputModel.date_from);
      const date_to = UtilitiesModule.dateFormatter(inputModel.date_to);

      // Set branch names asynchronously
      this.setBranchSubTitle(inputModel.branch_guids, date_from, date_to);
    }
    else {
      //this.createData();
    }
  }

  clear() {
    this.gridApi.setRowData([]);
    this.totalRecords = 0;
    this.rowData = [];
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

  setDataRowCache() {
    this.gridApi.setRowData(this.rowData);
  }

  getSalesCost(model: SalesReportByItemCodeModel) {
    return model[this.salesCost + '_amount'];
  }

  calculateTotal(node, key) {
    if (node.group) {
      let totalCost = 0;
      const children = node.childrenAfterGroup;

      children.forEach((childNode) => {
        const childCost = this.calculateTotal(childNode, key);
        totalCost += childCost;
      });

      return totalCost;
    } else {
      const rowData = node.data;
      return rowData && rowData[key] ? rowData[key] : 0;
    }
  }

  onCellClicked(e: any) {
    if (e.colDef.field === "server_doc_1") {
      this.openPopup(e.data);
    }
    else if (this.cellClickHandler) {
      this.cellClickHandler.onCellClicked(null,e.data.server_doc_1, e.data.server_doc_type);
    }
  }
  openPopup(documentData: any) {
    console.log("documentData", documentData);
    let data = typeof documentData === 'string' ? JSON.parse(documentData) : documentData;
    let dtoObject = {
      "server_doc_1" : data.server_doc_1,
      "server_doc_type" : data.server_doc_type,
    }
    this.subs.sink = this.genDocService.getByGenericCriteriaSnapshot(dtoObject,  this.apiVisa).subscribe(response=>{

      console.log('document data', response.data[0]);
      this.sessionStore.dispatch(SessionActions.selectDocument({document:response.data[0]}));
      this.dialog.open(AkaunGenericDocViewDialogComponent, {
        width: '80vw',
        data: response.data[0]
      });
    })
  }

  setBranchSubTitle(guids: string[], dateFrom: string, dateTo: string) {
    const key = 'branch';

    this.sessionStore.select(SessionSelectors.selectDropDownRowData).pipe(take(1)).subscribe(resolved => {
      let rowData = resolved ? resolved[key] ? resolved[key] : [] : [];
      let branchText = "ALL BRANCHES";

      if (rowData.length > 0 && guids && guids.length > 0) {
        const branches = rowData.filter(row => guids.includes(row.guid));
        if (branches.length > 0) {
          branchText = branches.map(branch => `${branch.name} (${branch.code})`).join(', ');
        }
      }

      let subTitle = branchText;
      subTitle += "\n";
      subTitle += `${dateFrom} to ${dateTo}`;

      const statusBarComponent = this.gridApi.getStatusPanel('statusBarExportKey');
      statusBarComponent.setSubTitle(subTitle);
    });
  }
}
