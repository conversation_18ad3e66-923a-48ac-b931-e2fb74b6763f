// Angular Core
import { ChangeDetectionStrategy, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';

// NgRx
import { ComponentStore } from '@ngrx/component-store';
import { Store } from '@ngrx/store';

// Third-party Libraries
import { GridOptions } from 'ag-grid-enterprise';
import { DocumentShortCodesClass, ErrorMessages } from 'blg-akaun-ts-lib';
import { ToastrService } from 'ngx-toastr';
import { Observable, from } from 'rxjs';
import { filter, map, mergeMap, take, toArray } from 'rxjs/operators';
import { SubSink } from 'subsink2';

// Shared Utilities - Models
import { ListingInputModel } from 'projects/shared-utilities/models/listing-input.model';
import { SearchQueryModel } from 'projects/shared-utilities/models/query.model';

// Shared Utilities - Components & Utilities
import { UtilitiesModule } from 'projects/shared-utilities/utilities/utilities.module';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { AppConfig } from 'projects/shared-utilities/visa';

// Session Controller
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionActions } from 'projects/shared-utilities/modules/session/session-controller/actions';

// Permissions
import { PermissionStates } from 'projects/shared-utilities/modules/permission/permission-controller';
import { UserPermInquirySelectors } from 'projects/shared-utilities/modules/permission/user-permissions-inquiry-controller/selectors';

// Application Imports
import { ApiService } from '../../../services/api-service';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { SalesReportByItemSalesmanCustomerCategorySearchModel } from '../../../models/advanced-search-models/sales-report-by-item-salesman-customer-category-search.model';
import { SalesReportByItemCodeInputModel } from '../../../models/sales-report-by-item-code-model';

interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
}

@Component({
  selector: 'app-sales-report-by-item-salesman-customer-category',
  templateUrl: './sales-report-by-item-salesman-customer-category.component.html',
  styleUrls: ['./sales-report-by-item-salesman-customer-category.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})
export class SalesReportByItemSalesmanCustomerCategoryComponent extends ViewColumnComponent implements OnInit, OnDestroy {

  protected subs = new SubSink();

  compId = 'salesReportByItemSalesmanCustomerCategory';
  compName = 'Sales Report by Item Salesman Customer Category';
  protected readonly index = 0;
  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  protected localState: LocalState;

  readonly deactivateAdd$ = this.componentStore.select(state => state.deactivateAdd);
  readonly deactivateList$ = this.componentStore.select(state => state.deactivateList);

  readonly userPermissionTarget$ = this.permissionStore.select(
    UserPermInquirySelectors.selectUserPermInquiry
  );

  toggleColumn$: Observable<boolean>;
  searchModel = SalesReportByItemSalesmanCustomerCategorySearchModel;

  showColumns = [
    { name: 'unit_cost', setting: 'HIDE_UNIT_COST', permission: 'SHOW_UNIT_COST' },
    { name: 'total_cost', setting: 'HIDE_MA_COST', permission: 'SHOW_MA_COST' }
  ]

  branchGuids: string[] = [];
  rowData = [];
  totalRecords = 0;
  salesCost = 'cost_ma';
  subTitle = '';
  searchQuery: SearchQueryModel;

  // api visa
  apiVisa = AppConfig.apiVisa;

  // initial grid state
  gridApi;
  gridColumnApi;

  gridOptions: GridOptions = {
    pagination: false,
    groupIncludeTotalFooter: true
  };

  columnsDefs = [];

  // Dynamic categories found in data
  dynamicCategories = new Set<string>();
  dynamicSubCategories = new Map<string, Set<string>>();

  readPermissionDefintion = {
    branch: 'API_TNT_DM_ERP_SALES_REPORT_BY_ITEM_CODE_READ'
  }

  constructor(
    protected viewColFacade: ViewColumnFacade,
    protected apiService: ApiService,
    protected toastr: ToastrService,
    protected readonly componentStore: ComponentStore<LocalState>,
    protected readonly permissionStore: Store<PermissionStates>,
    protected readonly sessionStore: Store<SessionStates>) {
    super();
  }

  ngOnInit() {
    // Initialize branch GUIDs based on permissions
    this.branchGuids = UtilitiesModule.getTargetsByPermission(this.userPermissionTarget$,
      this.readPermissionDefintion.branch, 'bl_fi_mst_branch'
    );
    this.toggleColumn$ = this.viewColFacade.toggleColumn$;
    this.subs.sink = this.viewColFacade.rowDataByItemSalesmanCustomerCategory$.subscribe(rowData => this.rowData = rowData);
    this.subs.sink = this.viewColFacade.totalRecordsByItemSalesmanCustomerCategory$.subscribe(totalRecords => this.totalRecords = totalRecords);
    this.subs.sink = this.localState$.subscribe(a => {
      this.localState = a;
      this.componentStore.setState(a);
    });
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.closeToolPanel();

    if (this.rowData.length > 0) {
      this.setDataRowCache();
    }
  }

  createData(inputModel?: SalesReportByItemCodeInputModel) {
    this.clear();
    this.subs.sink = this.apiService.getSalesReportBySalesmanCustomerCategory(inputModel, this.apiVisa).pipe(
      mergeMap(a =>
        from(a.data).pipe(
          map(b => {
            Object.assign(b, {
              // inv: DocumentShortCodesClass.serverDocTypeToShortCodeMapper(b.server_doc_type) + b.server_doc_1,
              // unit_price_net: UtilitiesModule.divide(b.line_amount_txn, b.line_quantity_base),
              // net_price_exclude_tax: b.line_amount_txn - b.line_amount_tax_gst,
              // unit_cost: b.line_cost_ma_price_company,        
            });
            return b;
          }),
          toArray(),
          map(c => {
            a.data = c;
            return a;
          })
        )
      )
    ).subscribe(resolved => {
      console.log('Raw data:', resolved);
      this.totalRecords = resolved.data.length;

      // Process and transform data for hierarchical display
      // const processedData = this.processDataForHierarchicalDisplay(resolved.data);

      console.log('Processed data:', processedData);
      console.log('Column definitions:', this.columnsDefs);

      this.rowData = [...this.rowData, ...processedData];

      // Update column definitions based on discovered categories
      this.updateColumnDefinitions();

      // Update grid with new column definitions and data
      if (this.gridApi) {
        this.gridApi.setColumnDefs(this.columnsDefs);
        this.gridApi.setRowData(this.rowData);
      }

      this.viewColFacade.selectRowDataSalesReportByItemSalesmanCustomerCategory(this.rowData);
      this.viewColFacade.selectTotalRecordsSalesReportByItemSalesmanCustomerCategory(this.totalRecords);
    }, err => {
      console.error(err);
      let msg = err.status===403 ? ErrorMessages.CRUD_403_ERRORS.READ : err.message;
      this.toastr.error(
        msg,
        "AG Grid Error",
        {
          tapToDismiss: true,
          progressBar: true,
          timeOut: 1300
        }
      );
      this.viewColFacade.loadFailed(err);
    });
  };

  onToggle(e: boolean) {
    this.viewColFacade.toggleColumn(e);
  }

  onSearch(e: SearchQueryModel) {
    console.log("search", e);
    if (!e.isEmpty) {
      if (e.keyword && e.keyword.length > 0 && e.keyword.length < 3)  {
        this.toastr.error(
          'Search keyword must more than 2 characters.',
          'Keyword',
          {
            tapToDismiss: true,
            progressBar: true,
            timeOut: 1300
          }
        );
        return;
      } 
      
      const inputModel = {} as SalesReportByItemCodeInputModel;
      inputModel.keyword = e.keyword;
      inputModel.date_from = '2022-01-01T00:00:00.000Z';
      inputModel.date_to = '2099-12-31T00:00:00.000Z';
      inputModel.branch_guids = this.branchGuids ?? [];
      if (e.queryString) {
        inputModel.keyword = UtilitiesModule.checkNull(e.queryString['itemCode'],'');
        inputModel.branch_guids = UtilitiesModule.checkNull(e.queryString['branch'],[]);
        inputModel.date_from = UtilitiesModule.checkNull(e.queryString['date']['from'],'2022-01-01T00:00:00.000Z');
        inputModel.date_to = UtilitiesModule.checkNull(e.queryString['date']['to'],'2099-12-31T00:00:00.000Z');
        inputModel.salesman_guids = UtilitiesModule.checkNull(e.queryString['salesman'],[]);

        const rawGroupBy1 = UtilitiesModule.checkNull(e.queryString['groupBy1'],'level 1');
        const rawGroupBy2 = UtilitiesModule.checkNull(e.queryString['groupBy2'],'level 2');
        const groupBy1 = this.getLabelHeaderGuid(rawGroupBy1);
        const groupBy2 = this.getLabelHeaderGuid(rawGroupBy2);
        inputModel.group_by = [groupBy1, groupBy2];

        this.salesCost = UtilitiesModule.checkNull(e.queryString['calculateBaseOn'],'cost_ma');
      }
      inputModel.server_doc_type = ['INTERNAL_SALES_CASHBILL','INTERNAL_SALES_INVOICE','INTERNAL_SALES_RETURN'];
      this.createData(inputModel);

      const date_from = UtilitiesModule.dateFormatter(inputModel.date_from);
      const date_to = UtilitiesModule.dateFormatter(inputModel.date_to);

      // Set branch names asynchronously
      this.setBranchSubTitle(inputModel.branch_guids, date_from, date_to);
    }
  }

  clear() {
    if (this.gridApi) {
      this.gridApi.setRowData([]);
    }
    this.totalRecords = 0;
    this.rowData = [];

    // Reset dynamic categories
    this.dynamicCategories.clear();
    this.dynamicSubCategories.clear();
    this.columnsDefs = [];
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

  setDataRowCache() {
    this.gridApi.setRowData(this.rowData);
  }

  getSalesCost(model) {
    return model[this.salesCost + '_amount'];
  }

  setBranchSubTitle(guids: string[], dateFrom: string, dateTo: string) {
    const key = 'branch';

    this.sessionStore.select(SessionSelectors.selectDropDownRowData).pipe(take(1)).subscribe(resolved => {
      let rowData = resolved ? resolved[key] ? resolved[key] : [] : [];
      let branchText = "ALL BRANCHES";

      if (rowData.length > 0 && guids && guids.length > 0) {
        const branches = rowData.filter(row => guids.includes(row.guid));
        if (branches.length > 0) {
          branchText = branches.map(branch => `${branch.name} (${branch.code})`).join(', ');
        }
      }

      this.subTitle = branchText;
      this.subTitle += "\n";
      this.subTitle += `${dateFrom} to ${dateTo}`;

      const statusBarComponent = this.gridApi.getStatusPanel('statusBarExportKey');
      statusBarComponent.setSubTitle(this.subTitle);
    });
  }

  getLabelHeaderGuid(level: string): string {
    for (let i = 1; i <= 10; i++) {
      if (level === `Level ${i}`) {
        return `label_hdr_${i}_guid`;
      }
    }
    return level; // fallback if not matched
  }

  /**
   * Process raw data into hierarchical structure for AG Grid display
   */
  processDataForHierarchicalDisplay(rawData: any[]): any[] {
    // First, discover all categories and subcategories
    this.discoverCategories(rawData);

    // Group data by salesman and customer
    const groupedData = this.groupBySalesmanAndCustomer(rawData);

    // Transform grouped data into grid rows
    return this.transformToGridRows(groupedData);
  }

  /**
   * Discover all unique categories and subcategories from the data
   */
  private discoverCategories(data: any[]): void {
    this.dynamicCategories.clear();
    this.dynamicSubCategories.clear();

    data.forEach(item => {
      if (item.cat1_name) {
        this.dynamicCategories.add(item.cat1_name);

        if (!this.dynamicSubCategories.has(item.cat1_name)) {
          this.dynamicSubCategories.set(item.cat1_name, new Set<string>());
        }

        if (item.cat2_name) {
          this.dynamicSubCategories.get(item.cat1_name).add(item.cat2_name);
        }
      }
    });
  }

  /**
   * Group data by salesman and customer combination
   */
  private groupBySalesmanAndCustomer(data: any[]): Map<string, any[]> {
    const grouped = new Map<string, any[]>();

    data.forEach(item => {
      const key = `${item.salesman_code || 'N/A'}_${item.customer_code || 'N/A'}`;

      if (!grouped.has(key)) {
        grouped.set(key, []);
      }

      grouped.get(key).push(item);
    });

    return grouped;
  }

  /**
   * Transform grouped data into AG Grid row format
   */
  private transformToGridRows(groupedData: Map<string, any[]>): any[] {
    const gridRows: any[] = [];

    groupedData.forEach((items, key) => {
      const firstItem = items[0];

      // Create base row structure
      const row: any = {
        salesman_code: firstItem.salesman_code || 'N/A',
        salesman_name: firstItem.salesman_name || 'N/A',
        customer_code: firstItem.customer_code || 'N/A',
        customer_name: firstItem.customer_name || 'N/A',
        total_amount: 0,
        total_cost: 0,
        gp: 0
      };

      // Initialize category columns
      this.dynamicCategories.forEach(category => {
        row[`${category}_total`] = 0;

        // Initialize subcategory columns
        const subCategories = this.dynamicSubCategories.get(category);
        if (subCategories) {
          subCategories.forEach(subCategory => {
            row[`${category}_${subCategory}`] = 0;
          });
        }
      });

      // Aggregate amounts by categories
      items.forEach(item => {
        const amount = item.amount_net || 0;
        const cost = this.getSalesCost(item) || 0;

        row.total_amount += amount;
        row.total_cost += cost;

        if (item.cat1_name) {
          row[`${item.cat1_name}_total`] += amount;

          if (item.cat2_name) {
            row[`${item.cat1_name}_${item.cat2_name}`] += amount;
          }
        }
      });

      // Calculate GP (Gross Profit)
      row.gp = row.total_amount - row.total_cost;

      gridRows.push(row);
    });

    return gridRows;
  }

  /**
   * Update column definitions based on discovered categories
   */
  private updateColumnDefinitions(): void {
    this.columnsDefs = [
      {
        headerName: 'Salesman',
        field: 'salesman_code',
        type: 'textColumn',
        pinned: 'left',
        width: 120
      },
      {
        headerName: 'Customer',
        field: 'customer_code',
        type: 'textColumn',
        pinned: 'left',
        width: 120
      }
    ];

    // Add category columns with subcategories
    this.dynamicCategories.forEach(category => {
      const subCategories = this.dynamicSubCategories.get(category);

      if (subCategories && subCategories.size > 0) {
        // Create column group for category with subcategories
        const children: any[] = [];

        subCategories.forEach(subCategory => {
          children.push({
            headerName: subCategory,
            field: `${category}_${subCategory}`,
            type: 'decimalColumn',
            width: 100
          });
        });

        this.columnsDefs.push({
          headerName: category,
          children: children
        });
      } else {
        // Simple category column without subcategories
        this.columnsDefs.push({
          headerName: category,
          field: `${category}_total`,
          type: 'decimalColumn',
          width: 100
        });
      }
    });

    // Add total amount and GP columns
    this.columnsDefs.push(
      {
        headerName: 'Total Amt',
        field: 'total_amount',
        type: 'decimalColumn',
        width: 120
      },
      {
        headerName: 'GP',
        field: 'gp',
        type: 'decimalColumn',
        width: 120
      }
    );
  }

  /**
   * Test method to verify data transformation with sample data
   */
  testDataTransformation() {
    const sampleData = [
      {
        "salesman_name": "Ida Amin",
        "cost_last_purchase_location": null,
        "cat1_name": null,
        "sales_max_price_amount": null,
        "rebate_price2_amount": 8,
        "rebate_price1_amount": 4,
        "rebate_price3_amount": 12,
        "ref_price2_amount": 168,
        "delta_price2_amount": 44,
        "cat2_name": null,
        "ref_price1_amount": 172,
        "cost_ma_amount": -9854.177639168696,
        "ref_price3_amount": 164,
        "landed_cost_wa_amount": null,
        "cost_lifo_amount": null,
        "cost_replacement_amount": 224,
        "customer_code": "1000198",
        "delta_price1_amount": 40,
        "landed_cost_ma_amount": null,
        "delta_price3_amount": 48,
        "landed_cost_fifo_amount": null,
        "amount_net": 290.01,
        "cat2_code": null,
        "salesman_code": "1000584",
        "cost_manual_amount": null,
        "sales_min_price_amount": null,
        "cost_fifo_amount": 0,
        "cat1_code": null,
        "purchase_min_price_amount": null,
        "cost_wa_amount": 0,
        "cost_last_purchase_company": null,
        "landed_cost_lifo_amount": null,
        "customer_name": "CASH",
        "purchase_max_price_amount": null
      },
      {
        "salesman_name": "Gracia Wavelet - Hello Smart",
        "cost_last_purchase_location": null,
        "cat1_name": "laptop",
        "sales_max_price_amount": 0,
        "rebate_price2_amount": 0,
        "rebate_price1_amount": 0,
        "rebate_price3_amount": 0,
        "ref_price2_amount": 0,
        "delta_price2_amount": 0,
        "cat2_name": "memory",
        "ref_price1_amount": 0,
        "cost_ma_amount": null,
        "ref_price3_amount": 0,
        "landed_cost_wa_amount": null,
        "cost_lifo_amount": null,
        "cost_replacement_amount": null,
        "customer_code": "1001266",
        "delta_price1_amount": 0,
        "landed_cost_ma_amount": null,
        "delta_price3_amount": 0,
        "landed_cost_fifo_amount": null,
        "amount_net": 0,
        "cat2_code": "memory",
        "salesman_code": "1000836",
        "cost_manual_amount": null,
        "sales_min_price_amount": 0,
        "cost_fifo_amount": null,
        "cat1_code": "laptop",
        "purchase_min_price_amount": null,
        "cost_wa_amount": null,
        "cost_last_purchase_company": null,
        "landed_cost_lifo_amount": null,
        "customer_name": "test",
        "purchase_max_price_amount": null
      },
      {
        "salesman_name": "Ida Amin",
        "cost_last_purchase_location": 0,
        "cat1_name": "laptop",
        "sales_max_price_amount": 0,
        "rebate_price2_amount": 0,
        "rebate_price1_amount": 0,
        "rebate_price3_amount": 0,
        "ref_price2_amount": 0,
        "delta_price2_amount": 0,
        "cat2_name": "hdd",
        "ref_price1_amount": 0,
        "cost_ma_amount": 27.610839205714633,
        "ref_price3_amount": 0,
        "landed_cost_wa_amount": 0,
        "cost_lifo_amount": null,
        "cost_replacement_amount": null,
        "customer_code": "TESTSHIP4",
        "delta_price1_amount": 0,
        "landed_cost_ma_amount": 0,
        "delta_price3_amount": 0,
        "landed_cost_fifo_amount": 0,
        "amount_net": 440,
        "cat2_code": "hdd",
        "salesman_code": "1000584",
        "cost_manual_amount": null,
        "sales_min_price_amount": 0,
        "cost_fifo_amount": 0,
        "cat1_code": "laptop",
        "purchase_min_price_amount": null,
        "cost_wa_amount": 0,
        "cost_last_purchase_company": 0,
        "landed_cost_lifo_amount": 0,
        "customer_name": "testship4",
        "purchase_max_price_amount": null
      }
    ];

    //console.log('Testing data transformation...');
    return this.processDataForHierarchicalDisplay(sampleData);
    //console.log('Processed data:', processedData);
    //console.log('Column definitions:', this.columnsDefs);
  }
}