// Angular Core
import { ChangeDetectionStrategy, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';

// NgRx
import { ComponentStore } from '@ngrx/component-store';
import { Store } from '@ngrx/store';

// Third-party Libraries
import { GridOptions } from 'ag-grid-enterprise';
import { DocumentShortCodesClass, ErrorMessages } from 'blg-akaun-ts-lib';
import { ToastrService } from 'ngx-toastr';
import { Observable, from } from 'rxjs';
import { filter, map, mergeMap, take, toArray } from 'rxjs/operators';
import { SubSink } from 'subsink2';

// Shared Utilities - Models
import { ListingInputModel } from 'projects/shared-utilities/models/listing-input.model';
import { SearchQueryModel } from 'projects/shared-utilities/models/query.model';

// Shared Utilities - Components & Utilities
import { UtilitiesModule } from 'projects/shared-utilities/utilities/utilities.module';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { AppConfig } from 'projects/shared-utilities/visa';

// Session Controller
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionActions } from 'projects/shared-utilities/modules/session/session-controller/actions';

// Permissions
import { PermissionStates } from 'projects/shared-utilities/modules/permission/permission-controller';
import { UserPermInquirySelectors } from 'projects/shared-utilities/modules/permission/user-permissions-inquiry-controller/selectors';

// Application Imports
import { ApiService } from '../../../services/api-service';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { SalesReportByItemSalesmanCustomerCategorySearchModel } from '../../../models/advanced-search-models/sales-report-by-item-salesman-customer-category-search.model';
import { SalesReportByItemCodeInputModel } from '../../../models/sales-report-by-item-code-model';

interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
}

@Component({
  selector: 'app-sales-report-by-item-salesman-customer-category',
  templateUrl: './sales-report-by-item-salesman-customer-category.component.html',
  styleUrls: ['./sales-report-by-item-salesman-customer-category.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})
export class SalesReportByItemSalesmanCustomerCategoryComponent extends ViewColumnComponent implements OnInit, OnDestroy {

  protected subs = new SubSink();

  compId = 'salesReportByItemSalesmanCustomerCategory';
  compName = 'Sales Report by Item Salesman Customer Category';
  protected readonly index = 0;
  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  protected localState: LocalState;

  readonly deactivateAdd$ = this.componentStore.select(state => state.deactivateAdd);
  readonly deactivateList$ = this.componentStore.select(state => state.deactivateList);

  readonly userPermissionTarget$ = this.permissionStore.select(
    UserPermInquirySelectors.selectUserPermInquiry
  );

  toggleColumn$: Observable<boolean>;
  searchModel = SalesReportByItemSalesmanCustomerCategorySearchModel;

  showColumns = [
    { name: 'unit_cost', setting: 'HIDE_UNIT_COST', permission: 'SHOW_UNIT_COST' },
    { name: 'total_cost', setting: 'HIDE_MA_COST', permission: 'SHOW_MA_COST' },
    { name: 'gp', setting: 'HIDE_GP', permission: 'SHOW_GP' },
    { name: 'gp_percentage', setting: 'HIDE_GP_PERCENTAGE', permission: 'SHOW_GP_PERCENTAGE' },
    { name: 'delta_unit_cost', setting: 'HIDE_DELTA_UNIT_COST', permission: 'SHOW_DELTA_UNIT_COST' },
    { name: 'delta_cost', setting: 'HIDE_DELTA_COST', permission: 'SHOW_DELTA_COST' },
    { name: 'delta_gp', setting: 'HIDE_DELTA_GP', permission: 'SHOW_DELTA_GP' },
    { name: 'delta_gp_percentage', setting: 'HIDE_DELTA_GP_PERCENTAGE', permission: 'SHOW_DELTA_GP_PERCENTAGE' },
  ]

  branchGuids: string[] = [];
  rowData = [];
  totalRecords = 0;
  salesCost = 'cost_ma';
  subTitle = '';
  searchQuery: SearchQueryModel;

  // api visa
  apiVisa = AppConfig.apiVisa;

  // initial grid state
  gridApi;
  gridColumnApi;

  gridOptions: GridOptions = {
    pagination: false,
    groupIncludeTotalFooter: true
  };

  columnsDefs = [
    {
      headerName: 'Salesman',
      field: 'salesman_display',
      type: 'textColumn',
    },
    {
      headerName: 'Customer',
      field: 'customer_display',
      type: 'textColumn',
    },
    {
      headerName: 'Total Amt',
      field: 'total_amount',
      type: 'decimalColumn'
    },
    {
      headerName: 'Cost',
      field: 'total_cost',
      type: 'decimalColumn'
    },
    {
      headerName: 'GP',
      field: 'gp',
      type: 'decimalColumn'
    }
  ];

  // Dynamic categories found in data
  dynamicCategories = new Set<string>();
  dynamicSubCategories = new Map<string, Set<string>>();

  readPermissionDefintion = {
    branch: 'API_TNT_DM_ERP_SALES_REPORT_BY_ITEM_CODE_READ'
  }

  constructor(
    protected viewColFacade: ViewColumnFacade,
    protected apiService: ApiService,
    protected toastr: ToastrService,
    protected readonly componentStore: ComponentStore<LocalState>,
    protected readonly permissionStore: Store<PermissionStates>,
    protected readonly sessionStore: Store<SessionStates>) {
    super();
  }

  ngOnInit() {
    // Initialize branch GUIDs based on permissions
    this.branchGuids = UtilitiesModule.getTargetsByPermission(this.userPermissionTarget$,
      this.readPermissionDefintion.branch, 'bl_fi_mst_branch'
    );
    this.toggleColumn$ = this.viewColFacade.toggleColumn$;
    this.subs.sink = this.viewColFacade.rowDataByItemSalesmanCustomerCategory$.subscribe(rowData => this.rowData = rowData);
    this.subs.sink = this.viewColFacade.totalRecordsByItemSalesmanCustomerCategory$.subscribe(totalRecords => this.totalRecords = totalRecords);
    this.subs.sink = this.localState$.subscribe(a => {
      this.localState = a;
      this.componentStore.setState(a);
    });
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.closeToolPanel();

    if (this.rowData.length > 0) {
      this.setDataRowCache();
    }
  }

  createData(inputModel?: SalesReportByItemCodeInputModel) {
    this.clear();
    this.subs.sink = this.apiService.getSalesReportBySalesmanCustomerCategory(inputModel, this.apiVisa).pipe(
      mergeMap(a =>
        from(a.data).pipe(
          map(b => {
            Object.assign(b, {
              // inv: DocumentShortCodesClass.serverDocTypeToShortCodeMapper(b.server_doc_type) + b.server_doc_1,
              // unit_price_net: UtilitiesModule.divide(b.line_amount_txn, b.line_quantity_base),
              // net_price_exclude_tax: b.line_amount_txn - b.line_amount_tax_gst,
              // unit_cost: b.line_cost_ma_price_company,        
            });
            return b;
          }),
          toArray(),
          map(c => {
            a.data = c;
            return a;
          })
        )
      )
    ).subscribe(resolved => {
      this.totalRecords = resolved.data.length;

      // Process and transform data for hierarchical display
      const processedData = this.processDataForHierarchicalDisplay(resolved.data);
      this.rowData = [...this.rowData, ...processedData];

      // Update column definitions based on discovered categories
      this.updateColumnDefinitions();

      // Update grid with new column definitions and data
      if (this.gridApi) {
        this.gridApi.setColumnDefs(this.columnsDefs);
        this.gridApi.setRowData(this.rowData);
      }

      this.viewColFacade.selectRowDataSalesReportByItemSalesmanCustomerCategory(this.rowData);
      this.viewColFacade.selectTotalRecordsSalesReportByItemSalesmanCustomerCategory(this.totalRecords);
    }, err => {
      console.error(err);
      let msg = err.status===403 ? ErrorMessages.CRUD_403_ERRORS.READ : err.message;
      this.toastr.error(
        msg,
        "AG Grid Error",
        {
          tapToDismiss: true,
          progressBar: true,
          timeOut: 1300
        }
      );
      this.viewColFacade.loadFailed(err);
    });
  };

  onToggle(e: boolean) {
    this.viewColFacade.toggleColumn(e);
  }

  onSearch(e: SearchQueryModel) {
    console.log("search", e);
    if (!e.isEmpty) {
      if (e.keyword && e.keyword.length > 0 && e.keyword.length < 3)  {
        this.toastr.error(
          'Search keyword must more than 2 characters.',
          'Keyword',
          {
            tapToDismiss: true,
            progressBar: true,
            timeOut: 1300
          }
        );
        return;
      } 
      
      const inputModel = {} as SalesReportByItemCodeInputModel;
      inputModel.keyword = e.keyword;
      inputModel.date_from = '2022-01-01T00:00:00.000Z';
      inputModel.date_to = '2099-12-31T00:00:00.000Z';
      inputModel.branch_guids = this.branchGuids ?? [];
      if (e.queryString) {
        inputModel.keyword = UtilitiesModule.checkNull(e.queryString['itemCode'],'');
        inputModel.branch_guids = UtilitiesModule.checkNull(e.queryString['branch'],[]);
        inputModel.date_from = UtilitiesModule.checkNull(e.queryString['date']['from'],'2022-01-01T00:00:00.000Z');
        inputModel.date_to = UtilitiesModule.checkNull(e.queryString['date']['to'],'2099-12-31T00:00:00.000Z');
        inputModel.salesman_guids = UtilitiesModule.checkNull(e.queryString['salesman'],[]);

        const rawGroupBy1 = UtilitiesModule.checkNull(e.queryString['groupBy1'],'level 1');
        const rawGroupBy2 = UtilitiesModule.checkNull(e.queryString['groupBy2'],'level 2');
        const groupBy1 = this.getLabelHeaderGuid(rawGroupBy1);
        const groupBy2 = this.getLabelHeaderGuid(rawGroupBy2);
        inputModel.group_by = [groupBy1, groupBy2];

        this.salesCost = UtilitiesModule.checkNull(e.queryString['calculateBaseOn'],'cost_ma');
      }
      inputModel.server_doc_type = ['INTERNAL_SALES_CASHBILL','INTERNAL_SALES_INVOICE','INTERNAL_SALES_RETURN'];
      this.createData(inputModel);

      const date_from = UtilitiesModule.dateFormatter(inputModel.date_from);
      const date_to = UtilitiesModule.dateFormatter(inputModel.date_to);

      // Set branch names asynchronously
      this.setBranchSubTitle(inputModel.branch_guids, date_from, date_to);
    }
  }

  clear() {
    if (this.gridApi) {
      this.gridApi.setRowData([]);
    }
    this.totalRecords = 0;
    this.rowData = [];

    // Reset dynamic categories
    this.dynamicCategories.clear();
    this.dynamicSubCategories.clear();
    this.columnsDefs = [];
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

  setDataRowCache() {
    this.gridApi.setRowData(this.rowData);
  }

  getSalesCost(model) {
    return model[this.salesCost + '_amount'];
  }

  setBranchSubTitle(guids: string[], dateFrom: string, dateTo: string) {
    const key = 'branch';

    this.sessionStore.select(SessionSelectors.selectDropDownRowData).pipe(take(1)).subscribe(resolved => {
      let rowData = resolved ? resolved[key] ? resolved[key] : [] : [];
      let branchText = "ALL BRANCHES";

      if (rowData.length > 0 && guids && guids.length > 0) {
        const branches = rowData.filter(row => guids.includes(row.guid));
        if (branches.length > 0) {
          branchText = branches.map(branch => `${branch.name} (${branch.code})`).join(', ');
        }
      }

      this.subTitle = branchText;
      this.subTitle += "\n";
      this.subTitle += `${dateFrom} to ${dateTo}`;

      const statusBarComponent = this.gridApi.getStatusPanel('statusBarExportKey');
      statusBarComponent.setSubTitle(this.subTitle);
    });
  }

  getLabelHeaderGuid(level: string): string {
    for (let i = 1; i <= 10; i++) {
      if (level === `Level ${i}`) {
        return `label_hdr_${i}_guid`;
      }
    }
    return level; // fallback if not matched
  }

  /**
   * Process raw data into hierarchical structure for AG Grid display
   */
  processDataForHierarchicalDisplay(rawData: any[]): any[] {
    // First, discover all categories and subcategories
    this.discoverCategories(rawData);

    // Group data by salesman and customer
    const groupedData = this.groupBySalesmanAndCustomer(rawData);

    // Transform grouped data into grid rows
    return this.transformToGridRows(groupedData);
  }

  /**
   * Discover all unique categories and subcategories from the data
   */
  discoverCategories(data: any[]): void {
    this.dynamicCategories.clear();
    this.dynamicSubCategories.clear();

    data.forEach(item => {
      // Always add category even if cat1_name is null (for blank columns)
      const cat1Name = item.cat1_name || '';
      this.dynamicCategories.add(cat1Name);

      if (!this.dynamicSubCategories.has(cat1Name)) {
        this.dynamicSubCategories.set(cat1Name, new Set<string>());
      }

      // Always add subcategory even if cat2_name is null (for blank columns)
      const cat2Name = item.cat2_name || '';
      this.dynamicSubCategories.get(cat1Name).add(cat2Name);
    });
  }

  /**
   * Group data by salesman and customer combination
   */
  groupBySalesmanAndCustomer(data: any[]): Map<string, any[]> {
    const grouped = new Map<string, any[]>();

    data.forEach(item => {
      const key = `${item.salesman_code || 'N/A'}_${item.customer_code || 'N/A'}`;

      if (!grouped.has(key)) {
        grouped.set(key, []);
      }

      grouped.get(key).push(item);
    });

    return grouped;
  }

  /**
   * Transform grouped data into AG Grid row format
   */
  transformToGridRows(groupedData: Map<string, any[]>): any[] {
    const gridRows: any[] = [];

    groupedData.forEach((items, key) => {
      const firstItem = items[0];

      // Create base row structure with formatted display values
      const row: any = {
        salesman_code: firstItem.salesman_code || 'N/A',
        salesman_name: firstItem.salesman_name || 'N/A',
        salesman_display: this.formatDisplayValue(firstItem.salesman_code, firstItem.salesman_name),
        customer_code: firstItem.customer_code || 'N/A',
        customer_name: firstItem.customer_name || 'N/A',
        customer_display: this.formatDisplayValue(firstItem.customer_code, firstItem.customer_name),
        total_amount: 0,
        total_cost: 0,
        gp: 0
      };

      // Initialize category columns
      this.dynamicCategories.forEach(category => {
        row[`${category}_total`] = 0;

        // Initialize subcategory columns
        const subCategories = this.dynamicSubCategories.get(category);
        if (subCategories) {
          subCategories.forEach(subCategory => {
            row[`${category}_${subCategory}`] = 0;
          });
        }
      });

      // Aggregate amounts by categories
      items.forEach(item => {
        const amount = item.amount_txn || 0;
        const cost = this.getSalesCost(item) || 0;

        row.total_amount += amount;
        row.total_cost += cost;

        // Use category name or 'Unknown' for null values
        const cat1Name = item.cat1_name || '';
        const cat2Name = item.cat2_name || '';

        row[`${cat1Name}_total`] += amount;
        row[`${cat1Name}_${cat2Name}`] += amount;
      });

      // Calculate GP (Gross Profit)
      row.gp = row.total_amount - row.total_cost;

      gridRows.push(row);
    });

    return gridRows;
  }

  /**
   * Format display value as [code]-[name] or just name if code is N/A
   */
  formatDisplayValue(code: string, name: string): string {
    const displayCode = code || 'N/A';
    const displayName = name || 'N/A';

    if (displayCode === 'N/A') {
      return displayName;
    }

    return `${displayCode}-${displayName}`;
  }

  /**
   * Update column definitions based on discovered categories
   */
  updateColumnDefinitions(): void {
    this.columnsDefs = [
      {
        headerName: 'Salesman',
        field: 'salesman_display',
        type: 'textColumn',
      },
      {
        headerName: 'Customer',
        field: 'customer_display',
        type: 'textColumn',
      }
    ];

    // Add category columns with subcategories
    this.dynamicCategories.forEach(category => {
      const subCategories = this.dynamicSubCategories.get(category);

      if (subCategories && subCategories.size > 0) {
        // Create column group for category with subcategories
        const children: any[] = [];

        subCategories.forEach(subCategory => {
          children.push({
            headerName: subCategory,
            field: `${category}_${subCategory}`,
            type: 'decimalColumn',
          });
        });

        this.columnsDefs.push({
          headerName: category,
          children: children
        });
      } else {
        // Simple category column without subcategories
        this.columnsDefs.push({
          headerName: category,
          field: `${category}_total`,
          type: 'decimalColumn',
        });
      }
    });

    // Add total amount and GP columns
    this.columnsDefs.push(
      {
        headerName: 'Total Amt',
        field: 'total_amount',
        type: 'decimalColumn'
      },
      {
        headerName: 'Cost',
        field: 'total_cost',
        type: 'decimalColumn'
      },
      {
        headerName: 'GP',
        field: 'gp',
        type: 'decimalColumn'
      }
    );
  }


}