import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';

import {
  ApiResponseModel,
  ApiVisa, Core2Config
} from 'blg-akaun-ts-lib';
import { UtilitiesModule } from 'projects/shared-utilities/utilities/utilities.module';
import { Observable } from 'rxjs';
import { ViewColumnFacade } from '../facades/view-column.facade';
import { SalesReportByItemCodeInputModel } from '../models/sales-report-by-item-code-model';

@Injectable({ providedIn: 'root' })
export class ApiService {
  readonly url = Core2Config.DOMAIN_URL + Core2Config.TENANT_DOMAIN_URL_PREFIX + Core2Config.ERP_PREFIX + Core2Config.REPORTS_PREFIX + 'sales';

  protected apiUrl: string;
  protected api_domain_url: string;
  protected endpoint_path: string;
  
  protected httpClient: HttpClient;

  constructor(http: HttpClient,
    protected viewColFacade: ViewColumnFacade,
  ) {
    this.apiUrl = this.url;
    this.endpoint_path = Core2Config.TENANT_DOMAIN_URL_PREFIX + Core2Config.ERP_PREFIX + Core2Config.REPORTS_PREFIX + 'sales';
    this.httpClient = http;
  }

  public getApiUrl(apiVisa: ApiVisa) {
    let url = this.apiUrl;
    if (this.endpoint_path && apiVisa.api_domain_url) {
      url = apiVisa.api_domain_url + this.endpoint_path;
    }
    return url;
  }

  public getHttpHeader(apiVisa: ApiVisa) {
    apiVisa.applet_code = apiVisa.applet_code ? apiVisa.applet_code : 'none';
    apiVisa.tenantCode = apiVisa.tenantCode ? apiVisa.tenantCode : '';

    const httpOptions = {
      headers: new HttpHeaders({
        authorization: apiVisa.jwt_secret,
        tenantCode: apiVisa.tenantCode, /// this will be removed in the future
        appId: apiVisa.applet_code, /// this will be removed in the future
      })
    };

    return httpOptions;
  }


  public getSalesReportByItemCode(inputModel: SalesReportByItemCodeInputModel, apiVisa: ApiVisa): Observable<ApiResponseModel<any>> {
    let url = this.getApiUrl(apiVisa);
    let formData = {
      "date_from": UtilitiesModule.getTodayNoTime().toISOString(),
      "date_to": UtilitiesModule.getTodayNoTime().toISOString(),
      // "branch_guids": [
      //   "2adb1d9c-5544-451d-9f08-75a442bef624"
      // ]
    };

    if (inputModel) formData = inputModel;

    url += "/sales-report-by-item-code";
    return this.httpClient.post<ApiResponseModel<any>>(
      url,
      formData,
      this.getHttpHeader(apiVisa)
    );
  }

  public getSalesReportByDocument(inputModel: SalesReportByItemCodeInputModel, apiVisa: ApiVisa): Observable<ApiResponseModel<any>> {
    let url = this.getApiUrl(apiVisa);
    let formData = {
      "date_from": UtilitiesModule.getTodayNoTime().toISOString(),
      "date_to": UtilitiesModule.getTodayNoTime().toISOString(),
      // "branch_guids": [
      //   "2adb1d9c-5544-451d-9f08-75a442bef624"
      // ]
    };

    if (inputModel) formData = inputModel;

    url += "/sales-report-by-document";
    return this.httpClient.post<ApiResponseModel<any>>(
      url,
      formData,
      this.getHttpHeader(apiVisa)
    );
  }

  public getHourlySalesReport(
    inputModel: SalesReportByItemCodeInputModel,
    apiVisa: ApiVisa
  ): Observable<ApiResponseModel<any>> {
    let url = this.getApiUrl(apiVisa);
    let formData = {
      date_from: UtilitiesModule.getTodayNoTime().toISOString(),
      date_to: UtilitiesModule.getTodayNoTime().toISOString(),
      // "branch_guids": [
      //   "2adb1d9c-5544-451d-9f08-75a442bef624"
      // ]
    };

    if (inputModel) formData = inputModel;

    url += "/hourly-sales-report";
    return this.httpClient.post<ApiResponseModel<any>>(
      url,
      formData,
      this.getHttpHeader(apiVisa)
    );
  }



  public getSalesReportByFC(inputModel: SalesReportByItemCodeInputModel, apiVisa: ApiVisa): Observable<ApiResponseModel<any>> {
    let url = this.getApiUrl(apiVisa);
    let formData = {
      "date_from": UtilitiesModule.getTodayNoTime().toISOString(),
      "date_to": UtilitiesModule.getTodayNoTime().toISOString(),
      // "branch_guids": [
      //   "2adb1d9c-5544-451d-9f08-75a442bef624"
      // ]
    };

    if (inputModel) formData = inputModel;

    url += "/sales-report-by-finance-charges";
    return this.httpClient.post<ApiResponseModel<any>>(
      url,
      formData,
      this.getHttpHeader(apiVisa)
    );
  }

  public getDailySalesReportCashflow(inputModel: any, apiVisa: ApiVisa): Observable<ApiResponseModel<any>> {
    let url = this.getApiUrl(apiVisa);
    let formData = {
      "date_from": UtilitiesModule.getTodayNoTime().toISOString(),
      "date_to": UtilitiesModule.getTodayNoTime().toISOString()
    };

    if (inputModel) formData = inputModel;

    url += "/daily-sales-report-cashflow-analysis";
    return this.httpClient.post<ApiResponseModel<any>>(
      url,
      formData,
      this.getHttpHeader(apiVisa)
    );
  }

  public getDailyGPBySalesman(inputModel: any, apiVisa: ApiVisa): Observable<ApiResponseModel<any>> {
    let url = this.getApiUrl(apiVisa);
    let formData = {
      "date_from": UtilitiesModule.getTodayNoTime().toISOString(),
      "date_to": UtilitiesModule.getTodayNoTime().toISOString()
    };

    if (inputModel) formData = inputModel;

    url += "/daily-gross-profit-by-salesman/backoffice-ep";
    return this.httpClient.post<ApiResponseModel<any>>(
      url,
      formData,
      this.getHttpHeader(apiVisa)
    );
  }

  public getCollectionWithInvoiceDetail(inputModel: any, apiVisa: ApiVisa): Observable<ApiResponseModel<any>> {
    let url = this.getApiUrl(apiVisa);
    let formData = {
      "date_from": UtilitiesModule.getTodayNoTime().toISOString(),
      "date_to": UtilitiesModule.getTodayNoTime().toISOString()
    };

    if (inputModel) formData = inputModel;

    url += "/collection-invoice-details/backoffice-ep";
    return this.httpClient.post<ApiResponseModel<any>>(
      url,
      formData,
      this.getHttpHeader(apiVisa)
    );
  }

  public getDailyCollectionSummary(inputModel: any, apiVisa: ApiVisa): Observable<ApiResponseModel<any>> {
    let url = this.getApiUrl(apiVisa);
    let formData = {
      "date_from": UtilitiesModule.getTodayNoTime().toISOString(),
      "date_to": UtilitiesModule.getTodayNoTime().toISOString()
    };

    if (inputModel) formData = inputModel;

    url += "/daily-collection-summary/backoffice-ep";
    return this.httpClient.post<ApiResponseModel<any>>(
      url,
      formData,
      this.getHttpHeader(apiVisa)
    );
  }

  public getMultiBranchSalesPurchaseCollection(inputModel: any, apiVisa: ApiVisa): Observable<ApiResponseModel<any>> {
    let url = this.getApiUrl(apiVisa);
    let formData = {
      "date_from": UtilitiesModule.getTodayNoTime().toISOString(),
      "date_to": UtilitiesModule.getTodayNoTime().toISOString()
    };

    if (inputModel) formData = inputModel;

    url += "/multi-branch-daily-sales-purchase-collection/backoffice-ep";
    return this.httpClient.post<ApiResponseModel<any>>(
      url,
      formData,
      this.getHttpHeader(apiVisa)
    );
  }

  public getReceiptWithCreditCard(inputModel: any, apiVisa: ApiVisa): Observable<ApiResponseModel<any>> {
    let url = this.getApiUrl(apiVisa);
    let formData = {
      "date_from": UtilitiesModule.getTodayNoTime().toISOString(),
      "date_to": UtilitiesModule.getTodayNoTime().toISOString()
    };

    if (inputModel) formData = inputModel;

    url += "/receipt-by-credit-card/backoffice-ep";
    return this.httpClient.post<ApiResponseModel<any>>(
      url,
      formData,
      this.getHttpHeader(apiVisa)
    );
  }

  public getDynamicReport(inputModel: any, apiVisa: ApiVisa): Observable<ApiResponseModel<any>> {
    let url = this.getApiUrl(apiVisa);
    url += "/dynamic-report/backoffice-ep";
    return this.httpClient.post<ApiResponseModel<any>>(
      url,
      inputModel,
      this.getHttpHeader(apiVisa)
    );
  }

   //@PostMapping(value = {"sales-report-by-salesman/backoffice-ep"})
   public getSalesReportBySalesman(inputModel: any, apiVisa: ApiVisa): Observable<ApiResponseModel<any>> {
    let url = this.getApiUrl(apiVisa);
    let formData = {
      "date_from": UtilitiesModule.getTodayNoTime().toISOString(),
      "date_to": UtilitiesModule.getTodayNoTime().toISOString()
    };

    if (inputModel) formData = inputModel;

    url += "/sales-report-by-salesman/backoffice-ep";
    return this.httpClient.post<ApiResponseModel<any>>(
      url,
      formData,
      this.getHttpHeader(apiVisa)
    );
  }

  public getSalesReportByDailyWeeklyMonthly(inputModel: any, apiVisa: ApiVisa): Observable<ApiResponseModel<any>> {
    let url = this.getApiUrl(apiVisa);
    let formData = {
      "date_from": UtilitiesModule.getTodayNoTime().toISOString(),
      "date_to": UtilitiesModule.getTodayNoTime().toISOString()
    };

    if (inputModel) formData = inputModel;

    url += "/sales-report-by-daily-weekly-monthly/backoffice-ep";
    return this.httpClient.post<ApiResponseModel<any>>(
      url,
      formData,
      this.getHttpHeader(apiVisa)
    );
  }

  public getSalesReportBySalesmanCustomerCategory(inputModel: any, apiVisa: ApiVisa): Observable<ApiResponseModel<any>> {
    let url = this.getApiUrl(apiVisa);
    let formData = {
      "date_from": UtilitiesModule.getTodayNoTime().toISOString(),
      "date_to": UtilitiesModule.getTodayNoTime().toISOString()
    };

    if (inputModel) formData = inputModel;

    url += "/sales-report-by-salesman-customer-category/backoffice-ep";
    return this.httpClient.post<ApiResponseModel<any>>(
      url,
      formData,
      this.getHttpHeader(apiVisa)
    );
  }
}
